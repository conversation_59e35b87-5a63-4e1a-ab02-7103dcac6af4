%% bare_jrnl.tex
%% V1.4b
%% 2015/08/26
%% by <PERSON>
%% see http://www.michaelshell.org/
%% for current contact information.
%%
%% This is a skeleton file demonstrating the use of IEEEtran.cls
%% (requires IEEEtran.cls version 1.8b or later) with an IEEE
%% journal paper.
%%
%% Support sites:
%% http://www.michaelshell.org/tex/ieeetran/
%% http://www.ctan.org/pkg/ieeetran
%% and
%% http://www.ieee.org/

%%*************************************************************************
%% Legal Notice:
%% This code is offered as-is without any warranty either expressed or
%% implied; without even the implied warranty of MERCHANTABILITY or
%% FITNESS FOR A PARTICULAR PURPOSE! 
%% User assumes all risk.
%% In no event shall the IEEE or any contributor to this code be liable for
%% any damages or losses, including, but not limited to, incidental,
%% consequential, or any other damages, resulting from the use or misuse
%% of any information contained here.
%%
%% All comments are the opinions of their respective authors and are not
%% necessarily endorsed by the IEEE.
%%
%% This work is distributed under the LaTeX Project Public License (LPPL)
%% ( http://www.latex-project.org/ ) version 1.3, and may be freely used,
%% distributed and modified. A copy of the LPPL, version 1.3, is included
%% in the base LaTeX documentation of all distributions of LaTeX released
%% 2003/12/01 or later.
%% Retain all contribution notices and credits.
%% ** Modified files should be clearly indicated as such, including  **
%% ** renaming them and changing author support contact information. **
%%*************************************************************************


% *** Authors should verify (and, if needed, correct) their LaTeX system  ***
% *** with the testflow diagnostic prior to trusting their LaTeX platform ***
% *** with production work. The IEEE's font choices and paper sizes can   ***
% *** trigger bugs that do not appear when using other class files.       ***                          ***
% The testflow support page is at:
% http://www.michaelshell.org/tex/testflow/



\documentclass[journal]{IEEEtran}
\usepackage{xeCJK}
\setCJKmainfont{Microsoft YaHei}
\setCJKsansfont{Microsoft YaHei}
\setCJKmonofont{Microsoft YaHei}
\usepackage[authoryear,round]{natbib}
\usepackage{float}
%
% If IEEEtran.cls has not been installed into the LaTeX system files,
% manually specify the path to it like:
% \documentclass[journal]{../sty/IEEEtran}





% Some very useful LaTeX packages include:
% (uncomment the ones you want to load)


% *** MISC UTILITY PACKAGES ***
%
%\usepackage{ifpdf}
% Heiko Oberdiek's ifpdf.sty is very useful if you need conditional
% compilation based on whether the output is pdf or dvi.
% usage:
% \ifpdf
%   % pdf code
% \else
%   % dvi code
% \fi
% The latest version of ifpdf.sty can be obtained from:
% http://www.ctan.org/pkg/ifpdf
% Also, note that IEEEtran.cls V1.7 and later provides a builtin
% \ifCLASSINFOpdf conditional that works the same way.
% When switching from latex to pdflatex and vice-versa, the compiler may
% have to be run twice to clear warning/error messages.






% *** CITATION PACKAGES ***
%
%\usepackage{cite}
% cite.sty was written by Donald Arseneau
% V1.6 and later of IEEEtran pre-defines the format of the cite.sty package
% \cite{} output to follow that of the IEEE. Loading the cite package will
% result in citation numbers being automatically sorted and properly
% "compressed/ranged". e.g., [1], [9], [2], [7], [5], [6] without using
% cite.sty will become [1], [2], [5]--[7], [9] using cite.sty. cite.sty's
% \cite will automatically add leading space, if needed. Use cite.sty's
% noadjust option (cite.sty V3.8 and later) if you want to turn this off
% such as if a citation ever needs to be enclosed in parenthesis.
% cite.sty is already installed on most LaTeX systems. Be sure and use
% version 5.0 (2009-03-20) and later if using hyperref.sty.
% The latest version can be obtained at:
% http://www.ctan.org/pkg/cite
% The documentation is contained in the cite.sty file itself.






% *** GRAPHICS RELATED PACKAGES ***
%
% Graphics package configuration for XeLaTeX
\usepackage{graphicx}
% XeLaTeX can handle PDF, PNG, JPG directly
\DeclareGraphicsExtensions{.pdf,.png,.jpg,.jpeg}
% graphicx was written by David Carlisle and Sebastian Rahtz. It is
% required if you want graphics, photos, etc. graphicx.sty is already
% installed on most LaTeX systems. The latest version and documentation
% can be obtained at: 
% http://www.ctan.org/pkg/graphicx
% Another good source of documentation is "Using Imported Graphics in
% LaTeX2e" by Keith Reckdahl which can be found at:
% http://www.ctan.org/pkg/epslatex
%
% latex, and pdflatex in dvi mode, support graphics in encapsulated
% postscript (.eps) format. pdflatex in pdf mode supports graphics
% in .pdf, .jpeg, .png and .mps (metapost) formats. Users should ensure
% that all non-photo figures use a vector format (.eps, .pdf, .mps) and
% not a bitmapped formats (.jpeg, .png). The IEEE frowns on bitmapped formats
% which can result in "jaggedy"/blurry rendering of lines and letters as
% well as large increases in file sizes.
%
% You can find documentation about the pdfTeX application at:
% http://www.tug.org/applications/pdftex





% *** MATH PACKAGES ***
%
\usepackage{amsmath}
% A popular package from the American Mathematical Society that provides
% many useful and powerful commands for dealing with mathematics.
%
% Note that the amsmath package sets \interdisplaylinepenalty to 10000
% thus preventing page breaks from occurring within multiline equations. Use:
%\interdisplaylinepenalty=2500
% after loading amsmath to restore such page breaks as IEEEtran.cls normally
% does. amsmath.sty is already installed on most LaTeX systems. The latest
% version and documentation can be obtained at:
% http://www.ctan.org/pkg/amsmath





% *** SPECIALIZED LIST PACKAGES ***
%
%\usepackage{algorithmic}
% algorithmic.sty was written by Peter Williams and Rogerio Brito.
% This package provides an algorithmic environment fo describing algorithms.
% You can use the algorithmic environment in-text or within a figure
% environment to provide for a floating algorithm. Do NOT use the algorithm
% floating environment provided by algorithm.sty (by the same authors) or
% algorithm2e.sty (by Christophe Fiorio) as the IEEE does not use dedicated
% algorithm float types and packages that provide these will not provide
% correct IEEE style captions. The latest version and documentation of
% algorithmic.sty can be obtained at:
% http://www.ctan.org/pkg/algorithms
% Also of interest may be the (relatively newer and more customizable)
% algorithmicx.sty package by Szasz Janos:
% http://www.ctan.org/pkg/algorithmicx




% *** ALIGNMENT PACKAGES ***
%
%\usepackage{array}
% Frank Mittelbach's and David Carlisle's array.sty patches and improves
% the standard LaTeX2e array and tabular environments to provide better
% appearance and additional user controls. As the default LaTeX2e table
% generation code is lacking to the point of almost being broken with
% respect to the quality of the end results, all users are strongly
% advised to use an enhanced (at the very least that provided by array.sty)
% set of table tools. array.sty is already installed on most systems. The
% latest version and documentation can be obtained at:
% http://www.ctan.org/pkg/array


% IEEEtran contains the IEEEeqnarray family of commands that can be used to
% generate multiline equations as well as matrices, tables, etc., of high
% quality.




% *** SUBFIGURE PACKAGES ***
%\ifCLASSOPTIONcompsoc
%  \usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
%\else
%  \usepackage[caption=false,font=footnotesize]{subfig}
%\fi
% subfig.sty, written by Steven Douglas Cochran, is the modern replacement
% for subfigure.sty, the latter of which is no longer maintained and is
% incompatible with some LaTeX packages including fixltx2e. However,
% subfig.sty requires and automatically loads Axel Sommerfeldt's caption.sty
% which will override IEEEtran.cls' handling of captions and this will result
% in non-IEEE style figure/table captions. To prevent this problem, be sure
% and invoke subfig.sty's "caption=false" package option (available since
% subfig.sty version 1.3, 2005/06/28) as this is will preserve IEEEtran.cls
% handling of captions.
% Note that the Computer Society format requires a larger sans serif font
% than the serif footnote size font used in traditional IEEE formatting
% and thus the need to invoke different subfig.sty package options depending
% on whether compsoc mode has been enabled.
%
% The latest version and documentation of subfig.sty can be obtained at:
% http://www.ctan.org/pkg/subfig




% *** FLOAT PACKAGES ***
%
%\usepackage{fixltx2e}
% fixltx2e, the successor to the earlier fix2col.sty, was written by
% Frank Mittelbach and David Carlisle. This package corrects a few problems
% in the LaTeX2e kernel, the most notable of which is that in current
% LaTeX2e releases, the ordering of single and double column floats is not
% guaranteed to be preserved. Thus, an unpatched LaTeX2e can allow a
% single column figure to be placed prior to an earlier double column
% figure.
% Be aware that LaTeX2e kernels dated 2015 and later have fixltx2e.sty's
% corrections already built into the system in which case a warning will
% be issued if an attempt is made to load fixltx2e.sty as it is no longer
% needed.
% The latest version and documentation can be found at:
% http://www.ctan.org/pkg/fixltx2e


%\usepackage{stfloats}
% stfloats.sty was written by Sigitas Tolusis. This package gives LaTeX2e
% the ability to do double column floats at the bottom of the page as well
% as the top. (e.g., "\begin{figure*}[!b]" is not normally possible in
% LaTeX2e). It also provides a command:
%\fnbelowfloat
% to enable the placement of footnotes below bottom floats (the standard
% LaTeX2e kernel puts them above bottom floats). This is an invasive package
% which rewrites many portions of the LaTeX2e float routines. It may not work
% with other packages that modify the LaTeX2e float routines. The latest
% version and documentation can be obtained at:
% http://www.ctan.org/pkg/stfloats
% Do not use the stfloats baselinefloat ability as the IEEE does not allow
% \baselineskip to stretch. Authors submitting work to the IEEE should note
% that the IEEE rarely uses double column equations and that authors should try
% to avoid such use. Do not be tempted to use the cuted.sty or midfloat.sty
% packages (also by Sigitas Tolusis) as the IEEE does not format its papers in
% such ways.
% Do not attempt to use stfloats with fixltx2e as they are incompatible.
% Instead, use Morten Hogholm'a dblfloatfix which combines the features
% of both fixltx2e and stfloats:
%
% \usepackage{dblfloatfix}
% The latest version can be found at:
% http://www.ctan.org/pkg/dblfloatfix




%\ifCLASSOPTIONcaptionsoff
%  \usepackage[nomarkers]{endfloat}
% \let\MYoriglatexcaption\caption
% \renewcommand{\caption}[2][\relax]{\MYoriglatexcaption[#2]{#2}}
%\fi
% endfloat.sty was written by James Darrell McCauley, Jeff Goldberg and 
% Axel Sommerfeldt. This package may be useful when used in conjunction with 
% IEEEtran.cls'  captionsoff option. Some IEEE journals/societies require that
% submissions have lists of figures/tables at the end of the paper and that
% figures/tables without any captions are placed on a page by themselves at
% the end of the document. If needed, the draftcls IEEEtran class option or
% \CLASSINPUTbaselinestretch interface can be used to increase the line
% spacing as well. Be sure and use the nomarkers option of endfloat to
% prevent endfloat from "marking" where the figures would have been placed
% in the text. The two hack lines of code above are a slight modification of
% that suggested by in the endfloat docs (section 8.4.1) to ensure that
% the full captions always appear in the list of figures/tables - even if
% the user used the short optional argument of \caption[]{}.
% IEEE papers do not typically make use of \caption[]'s optional argument,
% so this should not be an issue. A similar trick can be used to disable
% captions of packages such as subfig.sty that lack options to turn off
% the subcaptions:
% For subfig.sty:
% \let\MYorigsubfloat\subfloat
% \renewcommand{\subfloat}[2][\relax]{\MYorigsubfloat[]{#2}}
% However, the above trick will not work if both optional arguments of
% the \subfloat command are used. Furthermore, there needs to be a
% description of each subfigure *somewhere* and endfloat does not add
% subfigure captions to its list of figures. Thus, the best approach is to
% avoid the use of subfigure captions (many IEEE journals avoid them anyway)
% and instead reference/explain all the subfigures within the main caption.
% The latest version of endfloat.sty and its documentation can obtained at:
% http://www.ctan.org/pkg/endfloat
%
% The IEEEtran \ifCLASSOPTIONcaptionsoff conditional can also be used
% later in the document, say, to conditionally put the References on a 
% page by themselves.




% *** PDF, URL AND HYPERLINK PACKAGES ***
%
%\usepackage{url}
% url.sty was written by Donald Arseneau. It provides better support for
% handling and breaking URLs. url.sty is already installed on most LaTeX
% systems. The latest version and documentation can be obtained at:
% http://www.ctan.org/pkg/url
% Basically, \url{my_url_here}.




% *** Do not adjust lengths that control margins, column widths, etc. ***
% *** Do not use packages that alter fonts (such as pslatex).         ***
% There should be no need to do such things with IEEEtran.cls V1.6 and later.
% (Unless specifically asked to do so by the journal or conference you plan
% to submit to, of course. )


% correct bad hyphenation here
\hyphenation{op-tical net-works semi-conduc-tor}


\begin{document}
%
% paper title
% Titles are generally capitalized except for words such as a, an, and, as,
% at, but, by, for, in, nor, of, on, or, the, to and up, which are usually
% not capitalized unless they are the first or last word of the title.
% Linebreaks \\ can be used within to get better formatting as desired.
% Do not put math or special symbols in the title.
\title{超越静态上下文：一个关于动态指导与元学习局限性的探索}
%
%
% author names and IEEE memberships
% note positions of commas and nonbreaking spaces ( ~ ) LaTeX will not break
% a structure at a ~ so this keeps an author's name from being broken across
% two lines.
% use \thanks{} to gain access to the first footnote area
% a separate \thanks must be used for each paragraph as LaTeX2e's \thanks
% was not built to handle multiple paragraphs
%

\author{Anonymous~Author,~\IEEEmembership{Student,~IEEE,}
        Anonymous~Advisor,~\IEEEmembership{Member,~IEEE,}
        and~Anonymous~Collaborator,~\IEEEmembership{Member,~IEEE}}

% note the % following the last \IEEEmembership and also \thanks - 
% these prevent an unwanted space from occurring between the last author name
% and the end of the author line. i.e., if you had this:
% 
% \author{....lastname \thanks{...} \thanks{...} }
%                     ^------------^------------^----Do not want these spaces!
%
% a space would be appended to the last name and could cause every name on that
% line to be shifted left slightly. This is one of those "LaTeX things". For
% instance, "\textbf{A} \textbf{B}" will typeset as "A B" not "AB". To get
% "AB" then you have to do: "\textbf{A}\textbf{B}"
% \thanks is no different in this regard, so shield the last } of each \thanks
% that ends a line with a % and do not let a space in before the next \thanks.
% Spaces after \IEEEmembership other than the last one are OK (and needed) as
% you are supposed to have spaces between the names. For what it is worth,
% this is a minor point as most people would not even notice if the said evil
% space somehow managed to creep in.



% The paper headers
\markboth{IEEE Transactions on Pattern Analysis and Machine Intelligence,~Vol.~XX, No.~X, \today}%
{Anonymous \MakeLowercase{\textit{et al.}}: Beyond Static Context: Dynamic Guidance and Meta-Learning Limits for NER}
% The only time the second header will appear is for the odd numbered pages
% after the title page when using the twoside option.
% 
% *** Note that you probably will NOT want to include the author's ***
% *** name in the headers of peer review papers.                   ***
% You can use \ifCLASSOPTIONpeerreview for conditional compilation here if
% you desire.




% If you want to put a publisher's ID mark on the page you can do it like
% this:
%\IEEEpubid{0000--0000/00\$00.00~\copyright~2015 IEEE}
% Remember, if you use this you must call \IEEEpubidadjcol in the second
% column for its text to clear the IEEEpubid mark.



% use for special paper notices
%\IEEEspecialpapernotice{(Invited Paper)}




% make the title area
\maketitle

% As a general rule, do not put math, special symbols or citations
% in the abstract or keywords.
\begin{abstract}
近年来，基于上下文学习（ICL）的大语言模型（LLM）为少样本命名实体识别（NER）带来了新的范式。然而，现有ICL方法大多依赖静态或随机选择的示例，其主要作用是帮助LLM理解任务指令与格式，未能充分利用LLM的深度推理与归纳能力。更重要的是，它们缺乏针对每个输入动态调整指导策略的能力，也无法从过往的推理经验中学习和进化。在本文中，我们提出了一个名为DynamicNER的动态指导框架。首先，我们设计了一个数据预处理阶段DynamicNER-Base，通过“检索-过滤-精炼”管道，为每一个输入动态生成高度相关的“核心公理”，将LLM从一个被动的模式模仿者转变为主动的规则执行者。基于这一预处理阶段获得的增强数据，我们进一步探索了一个元学习框架DynamicNER-Meta，该框架通过分析历史成败经验来构建一个能够自我进化的指导系统。大量的实验表明，DynamicNER-Base预处理阶段显著提升了模型性能，超越了现有的SOTA方法，而DynamicNER-Meta通过引入历史经验分析进一步提升了性能。这项工作不仅为少样本NER提供了一个新的、性能卓越的数据预处理方法，也展示了元学习在ICL中的应用潜力。
\end{abstract}

% Note that keywords are not normally used for peerreview papers.
\begin{IEEEkeywords}
命名实体识别，上下文学习，动态指导，元学习，少样本学习，大语言模型。
\end{IEEEkeywords}






% For peer review papers, you can put extra information on the cover
% page as needed:
% \ifCLASSOPTIONpeerreview
% \begin{center} \bfseries EDICS Category: 3-BBND \end{center}
% \fi
%
% For peerreview papers, this IEEEtran command inserts a page break and
% creates the second title. It will be ignored for other modes.
\IEEEpeerreviewmaketitle



\section{引言(需要体现引出相关工作的)}

命名实体识别（NER）作为信息抽取的核心技术\citep{keraghel_recent_2024}，在机器翻译\citep{babych_improving_2003}、问答系统\citep{molla_named_nodate}等众多应用中发挥着关键作用。随着大语言模型（LLM）的快速发展，基于上下文学习（ICL）的少样本NER方法因其无需大规模微调的优势而备受关注。
尽管现有工作在ICL for NER方面取得了显著进展，但仍存在重要局限：首先，大多数方法依赖静态指导或简单的示例选择，缺乏针对每个输入动态调整指导策略的能力；其次，现有元学习方法主要关注模型架构优化，而非推理过程的自我进化；最后，对于元学习在ICL中的应用边界和挑战缺乏深入的实证分析。

P-ICL等工作\citep{ashok_promptner_2023}证明，精心设计的实体定义比零散示例更有效，这些方法通过离线扫描训练集提炼出通用的静态规则。(要换)

但静态指导存在根本局限：无法应对不同输入的独特挑战。(youhua)同时，研究界探索动态上下文构建，如迭代式演示选择（IDS）\citep{qin_-context_2024}利用思维链推理为每个输入动态选择示例。尽管IDS取得了进展，但仍局限于"示例选择"范畴。
本文认为ICL的潜力不止于"模仿"示例，更在于"理解"指导。受检索增强生成思想启发\citep{rodrigues_meta-prompting_2024}，我们提出DynamicNER框架。

DynamicNER-Base作为框架运作的第一个阶段，通过"检索-过滤-精炼"管道为每个输入挑选出更适合的"精选示例"，并即时生成量身定制的"核心公理"，实现从动态示例选择到动态规则生成的范式转变。

进一步，我们探索DynamicNER-Meta扩展框架，旨在回答"ICL系统能否从历史表现中学习并自我进化"这一根本问题。受对比学习\citep{mo_c-icl_2024}和元级别优化\citep{peng_metaie_2024}等工作启发，该框架通过分析历史成败案例构建元知识库，动态生成对抗性指导。

实验结果表明：首先，DynamicNER-Base预处理阶段显著超越P-ICL\citep{ashok_promptner_2023}和IDS\citep{qin_-context_2024}等方法，证明了动态规则生成预处理的优越性。更重要的是，DynamicNER-Meta通过引入历史经验分析和思维链引导，在DynamicNER-Base的基础上进一步提升了性能，展示了元学习在ICL中的有效性。




综上，本文的主要贡献在于：1) 提出了一个创新的动态指导生成框架DynamicNER，实现了从静态示例选择到动态规则生成的重要范式转变；2) 设计了"检索-过滤-精炼"三阶段管道，系统性地解决了ICL中指导质量和相关性的核心问题；3) 通过大量实验验证了动态公理生成相比传统方法的显著优势，为少样本NER建立了新的性能基准。

\begin{figure}[H]
\centering
\includegraphics[width=0.8\textwidth,height=6cm,keepaspectratio]{ner_task_example.png}
\caption{命名实体识别任务示例。图中展示了一个典型的NER任务，其中需要识别文本中的人名、地名、组织名等不同类型的实体。}
\label{fig:ner_example}
\end{figure}

\section{相关工作}

\subsection{大语言模型及其在命名实体识别中的应用}

大语言模型（Large Language Models, LLMs）\citep{brown2020languagemodelsfewshotlearners}在各种自然语言处理任务上取得了显著的性能提升。使用LLMs进行下游任务的策略主要分为两类：微调（fine-tuning）和上下文学习（In-context Learning, ICL）。

微调策略以预训练模型为初始化，在下游监督数据上运行额外的训练轮次，通过参数更新来适应特定任务。在命名实体识别领域，微调方法通常需要大量标注数据，但能够取得较好的性能表现。然而，微调方法在面对新领域或新实体类型时，往往需要重新收集标注数据并进行训练，这在实际应用中存在较大的成本和时间开销。

相比之下，上下文学习（ICL）展现了无需参数更新即可执行新任务的强大潜力，仅通过在输入中提供少量示例即可引导模型完成特定任务。在命名实体识别任务中，基于ICL的方法能够在少样本场景下取得良好的性能，这使得LLMs在资源受限的NER任务中具有重要的应用价值。特别是在处理新兴领域或低资源语言的NER任务时，ICL方法展现出了显著的优势。

\subsection{上下文学习及其在命名实体识别中的应用}

尽管LLMs在多种NLP任务上表现出色，但在命名实体识别任务上，基于ICL的方法仍远未达到微调方法的性能水平。ICL的性能对示例选择具有一定的敏感性，但这种敏感性的机制仍需深入研究。\citet{min_rethinking_2022}发现，即使随机替换示例中的标签，在多种分类任务上的性能也几乎不受影响，这表明示例的其他方面（如标签空间、输入文本分布和序列格式）才是性能的关键驱动因素。

针对ICL在NER任务中的挑战，研究者们从多个角度提出了改进策略。在示例选择策略方面，\citet{lee_good_2022}系统研究了示例策略，包括示例内容（实体示例，是否包含上下文）、选择方法和模板设计，发现合适的示例策略可以在低资源设置下显著提升性能。\citet{adiga_designing_2024}提出了基于复杂度的提示选择方法，通过句子级和词级指标来匹配示例与测试句子的语法语义复杂度。\citet{qin_-context_2024}提出了迭代示例选择（IDS）方法，通过零样本思维链推理来选择既多样又与测试样本强相关的示例。

在指导范式创新方面，\citet{ashok_promptner_2023}提出PromptNER，认为仅提供示例不足以让LLM理解特定领域中的实体定义，因此在提示中加入明确的实体类型定义。\citet{wang_gpt-ner_2023}提出GPT-NER，将传统的序列标注任务重构为文本生成任务，通过自然语言生成来完成实体识别，代表了生成式方法在传统NER中的重要探索。\citet{huang_guidener_nodate}进一步提出GuideNER，使用LLM生成简洁的标注指南作为上下文信息，显著优于使用示例的传统ICL方法。\citet{jiang_p-icl_2024}提出P-ICL框架，为每个实体类型提供代表性的实体词作为辅助信息，通过K-Means聚类选择最优的点实体。

在任务格式重构方面，\citet{ji_vicunaner_2023}提出了两阶段识别框架，通过"识别-再识别"的方式提高实体召回率。\citet{chen_self-icl_2023}提出Self-ICL，通过自生成示例实现零样本上下文学习。\citet{mo_c-icl_2024}提出对比上下文学习（C-ICL），同时利用正确和错误的样本构建示例，增强LLM的实体抽取能力。

\subsection{元学习方法在命名实体识别中的应用}

元学习方法在命名实体识别中的应用旨在提升模型的快速适应能力和泛化性能。\citet{chen_learning_2023}提出了一种基于上下文学习的NER方法，将预训练语言模型建模为元函数，通过元函数预训练算法显式地为PLM注入上下文学习能力，而非依赖隐式涌现。该方法能够有效处理新实体类型的涌现和高质量标注的缺乏问题。

\citet{peng_metaie_2024}提出MetaIE框架，通过从LLM中蒸馏出一个小型元模型来处理各种信息抽取任务。该方法将IE任务形式化为标签到跨度的匹配问题，通过符号蒸馏获得具有通用IE能力的元模型。\citet{wang_cooperative_2025}提出了协作多智能体系统（CMAS），将零样本NER任务构建为多智能体协作问题，通过四个专门的智能体（自标注器、类型相关特征提取器、示例判别器和总预测器）的协作来提升整体性能。

这些元学习方法的共同目标是构建能够快速适应新任务和新领域的通用模型，为少样本和零样本NER提供了新的解决思路。然而，现有方法在示例选择的精确性和适应性方面仍存在改进空间，特别是在如何更好地利用检索到的示例信息方面。本文提出的方法正是针对这一挑战，通过动态指导生成和元学习增强来提升ICL在NER任务中的性能。

\section{方法论}

\subsection{框架总览}

我们的方法\texttt{DynamicNER}包含两个递进的组件：\texttt{DynamicNER-Base}，作为数据预处理阶段提供动态指导生成；\texttt{DynamicNER-Meta}，探索元学习增强。预处理阶段通过一个**形式化为"检索-过滤-精炼"三阶段的管道**运行，为每个输入生成定制化的指导。元学习框架基于预处理阶段获得的增强数据构建，通过整合历史经验分析来动态生成对抗性策略，以期实现持续的性能演进。

% Model architecture figure
\begin{figure}[!t]
\centering
\includegraphics[width=3.5in]{model.png}
\caption{DynamicNER 放3 里面  框架总体架构图。展示了DynamicNER-Base的"检索-过滤-精炼"三阶段管道和DynamicNER-Meta的元学习增强机制。}
\label{fig:model_architecture}
\end{figure}

\subsection{DynamicNER-Base}

\texttt{DynamicNER-Base}预处理阶段的目标是为每个输入句子 $X$ 动态生成一组最优的核心公理 $\mathcal{A}$，从而获得增强的训练数据。

\subsubsection{检索增强的上下文选择}

我们框架的第一个组件实现了一个复杂的上下文选择机制。给定一个输入文本 $X$ 和一个完整的训练语料库 $\mathcal{D}_{\text{train}}$，我们采用两阶段检索过程：

\textbf{语义检索 (Semantic Retrieval)}：我们首先使用一个预训练的向量编码器 $f_{\text{enc}}(\cdot)$（本文中使用BAAI/bge-m3）将输入句子 $X$ 和训练集 $\mathcal{D}_{\text{train}}$ 中的每个句子 $X_j$ 映射到高维向量空间。然后，我们通过计算**余弦相似度**来识别与 $X$ 最相似的 $k$ 个候选示例(公式需要编号)，形成候选集 $\mathcal{C}$：
$$
\mathcal{C} = \underset{\{X_j\}_{j=1}^k \subset \mathcal{D}_{\text{train}}}{\text{argmax}} \sum_{j=1}^k \text{sim}(f_{\text{enc}}(X), f_{\text{enc}}(X_j))
$$
其中 $\text{sim}(\cdot, \cdot)$ 代表余弦相似度函数。

\textbf{基于LLM的过滤 (LLM-based Filtering)}：检索到的候选项 $\mathcal{C}$ 并不直接使用，而是通过一个基于LLM的过滤机制进行筛选。我们构建一个提示 $P_{\text{filter}}$，要求LLM $f_{\text{LLM}}$ 评估 $\mathcal{C}$ 中每个示例对 $X$ 的相关性、多样性和"教学价值"。最终得到一个更小的、经过智能筛选的示例集 $\mathcal{C}_{\text{sel}}$：
$$
\mathcal{C}_{\text{sel}} = f_{\text{LLM}}(P_{\text{filter}}(X, \mathcal{C}))
$$
这种方法确保所选示例不仅在语义上相似，而且对目标预测任务具有战略价值。

\subsubsection{核心公理生成}

在获得高质量的示例集 $\mathcal{C}_{\text{sel}}$ 后，我们通过一个专门的LLM精炼过程来生成输入特定的"核心公理" $\mathcal{A}$。我们构建另一个提示 $P_{\text{distill}}$，要求LLM从 $\mathcal{C}_{\text{sel}}$ 中**抽象和提炼**出与 $X$ 相关的基本模式和规则：
$$\mathcal{A} = f_{\text{LLM}}(P_{\text{distill}}(X, \mathcal{C}_{\text{sel}}))$$
与先前方法中使用的静态定义不同，这组公理 $\mathcal{A}$ 是为解决每个输入文本 $X$ 的特定挑战和特征而**实时定制**的。

\subsection{DynamicNER-Meta：元学习进化扩展}

\subsubsection{经验积累与元知识库构建}

元学习扩展在两种模式下运行：\texttt{build\_meta}和\texttt{run\_with\_meta}。在\texttt{build\_meta}阶段，系统在开发集 $\mathcal{D}_{\text{dev}}$ 上运行\texttt{DynamicNER-Base}预处理阶段，并构建一个元知识库 $\mathcal{D}_{\text{meta}}$。这个过程包括：

\begin{enumerate}
\item 对每个开发集样本 $X_i \in \mathcal{D}_{\text{dev}}$，记录其生成的指导 $\mathcal{G}_i$、模型的预测 $Y_{\text{pred},i}$ 和真实标签 $Y_{\text{true},i}$。
\item 计算每个预测的性能分数，例如F1分数：
    $$
    F1_i = \frac{2 \cdot P_i \cdot R_i}{P_i + R_i}
    $$
    其中 $P_i$ 和 $R_i$ 分别是第 $i$ 个样本的精确率和召回率。
\item 将完整的经验元组 $(X_i, \mathcal{G}_i, Y_{\text{pred},i}, Y_{\text{true},i}, F1_i)$ 存入元知识库 $\mathcal{D}_{\text{meta}}$。
\end{enumerate}

\subsubsection{动态对抗策略生成}

在\texttt{run\_with\_meta}模式下，当处理新的测试句子 $X_{\text{new}}$ 时，系统首先从 $\mathcal{D}_{\text{meta}}$ 中检索出与 $X_{\text{new}}$ 最相似的一组历史成功案例 $\mathcal{E}_{\text{pos}}$ 和失败案例 $\mathcal{E}_{\text{neg}}$。然后，系统采用协同工作的双分析器，并在两个阶段都使用思维链（Chain-of-Thought, CoT）引导来增强推理能力：

\begin{itemize}
\item \textbf{成功分析器}: $f_{\text{LLM}}(P_{\text{success}}^{\text{CoT}}(\mathcal{E}_{\text{pos}}, X_{\text{new}})) \rightarrow \mathcal{G}_{\text{pos}}$
\item \textbf{失败分析器}: $f_{\text{LLM}}(P_{\text{failure}}^{\text{CoT}}(\mathcal{E}_{\text{neg}}, X_{\text{new}})) \rightarrow \mathcal{G}_{\text{neg}}$
\end{itemize}

其中，$P_{\text{success}}^{\text{CoT}}$ 和 $P_{\text{failure}}^{\text{CoT}}$ 分别是包含思维链引导的提示模板，指导LLM进行逐步推理分析。这种思维链引导帮助模型更深入地理解历史经验的成功和失败模式，从而生成更精准的策略指导。

最终生成的动态对抗策略 $\mathcal{G}_{\text{final}}$ 是这两部分指导的结合，旨在最大化成功的可能性，同时最小化常见失败模式的风险。

\section{实验设置}

\subsection{数据集和评估指标}

我们在几个标准NER基准上评估我们的方法，包括**CoNLL-2003**、**OntoNotes 5.0**以及一些领域特定数据集（例如ACE2005，用于检验嵌套NER能力）。我们使用标准的实体级精确率（Precision）、召回率（Recall）和F1分数作为主要评估指标，遵循严格的边界和类型双重匹配标准。

具体而言，对于每个实体类型 $t$，我们计算：
$$
P_t = \frac{|\text{预测正确的类型}t\text{实体}|}{|\text{预测的类型}t\text{实体}|}
$$
$$
R_t = \frac{|\text{预测正确的类型}t\text{实体}|}{|\text{真实的类型}t\text{实体}|}
$$
$$
F1_t = \frac{2 \cdot P_t \cdot R_t}{P_t + R_t}
$$

最终的宏平均F1分数定义为：
$$
F1_{\text{macro}} = \frac{1}{|T|} \sum_{t \in T} F1_t
$$
其中 $T$ 是所有实体类型的集合。

\subsection{基线模型}

我们将我们的方法与几种有代表性的ICL方法进行比较，以全面评估其性能：

\begin{itemize}
\item \textbf{Random ICL}: 使用从训练集中随机选择的示例作为上下文，代表了ICL的性能下限。
\item \textbf{kNN-ICL}: 采用基于k-近邻的检索来选择示例，代表了主流的"动态示例选择"方法。
\item \textbf{P-ICL}: 一种通过提供代表性"点实体"而非完整句子来增强指导的方法，采用5-shot + 10 point-shot设置，用于对比不同的上下文简化策略。
\item \textbf{GPT-NER}: 一种通过离线提炼静态指导原则来引导模型的方法，用于对比"动态指导"与"静态指导"。
\item \textbf{C-ICL}: 一种引入反例进行对比学习的方法，用于对比不同的高级ICL探索。
\end{itemize}

\subsection{实现细节}

我们的实验使用\texttt{Qwen3-8b}作为主要的骨干LLM。选择\texttt{qwen3-8b}主要基于其性能表现优异且支持本地部署。对于检索阶段，我们采用\texttt{BAAI/bge-m3}作为向量编码器进行语义相似性计算，该模型在多语言文本表示方面表现优异，特别适合我们的中英文混合NER任务。元知识库使用包含20\%训练数据的开发集构建，以确保有足够多样性的历史案例。所有实验均在\textbf{5-shot}设置下进行，除非另有说明。LLM的生成参数设置为：\texttt{temperature=0.1}, \texttt{top\_p=1.0}。

对于检索阶段的超参数，我们设置候选集大小 $k=10$，经过LLM过滤后的最终示例集大小为 $|\mathcal{C}_{\text{sel}}|=5$。在元学习阶段，我们从元知识库中检索 $|\mathcal{E}_{\text{pos}}|=3$ 个成功案例和 $|\mathcal{E}_{\text{neg}}|=3$ 个失败案例进行分析。在DynamicNER-Meta的两个关键阶段（build\_meta和run\_with\_meta）中，我们都采用了思维链（CoT）引导的提示词设计，通过"让我们逐步思考"等引导语句促使LLM进行更深入的推理分析。

\subsection{模型变体(简单介绍一下)}

为了清晰地展示我们框架各部分的贡献，我们报告了两个主要模型变体的结果：

\begin{itemize}
\item \textbf{\texttt{DynamicNER-Base}}: 数据预处理阶段，使用动态公理生成。
\item \textbf{\texttt{DynamicNER-Meta}}: 基于\texttt{Base}预处理阶段获得的数据，加入了元学习增强。
\end{itemize}

这使我们能够将动态指导生成的效果与元学习增强的效果分离开来，进行深入的消融和性能分析。

\subsection{消融实验设置}

为了验证DynamicNER-Base预处理阶段各组件的有效性，我们设计了以下消融实验变体：

\begin{itemize}
\item \textbf{w/o 检索增强}：移除检索增强组件，使用随机示例选择替代语义检索过程。
\item \textbf{w/o LLM过滤}：保留语义检索但移除基于LLM的过滤机制（Query 1），直接使用检索到的候选集。
\item \textbf{w/o 动态公理生成}：移除动态公理生成组件（Query 2），使用静态指导原则替代。
\item \textbf{完整DynamicNER-Base}：包含所有组件的完整预处理阶段。
\end{itemize}

所有消融实验均在相同的实验条件下进行，使用相同的基模型、超参数设置和评估指标，以确保结果的可比性。

\section{结果与分析}

\subsection{主要结果与基础框架的有效性}

表1展示了我们的方法与所有基线方法在多个数据集上的全面比较。结果展示了几个关键发现：

% Main results table
\begin{table}[!t]
\renewcommand{\arraystretch}{1.2}
\caption{主要结果：NER基准上的F1分数（\%）}
\label{tab:main_results}
\centering
\small
\begin{tabular}{|p{1.8cm}|p{1.5cm}|p{1.2cm}|p{1.2cm}|p{1.0cm}|p{1.0cm}|}
\hline
\textbf{方法} & \textbf{Backbone} & \textbf{CoNLL-2003} & \textbf{OntoNotes 5.0} & \textbf{ACE04} & \textbf{平均值} \\
\hline
Random ICL & qwen3-8b & 0.0 & 0.0 & 0.0 & 0.0 \\
kNN-ICL & qwen3-8b & 77.92 & 0.0 & 0.0 & 0.0 \\
P-ICL(5+10 shot) & LLaMA-3-70B & 78.95 & 0.0 & 0.0 & 0.0 \\
GPT-NER(32shot) & GPT3+sent-emb & 83.73 & 0.0 & 88.07 & 0.0 \\
C-ICL & CodeLlama-34B & 87.36 & 0.0 & 54.47 & 0.0 \\
\hline
DynamicNER-Base & qwen3-8b & \textbf{89.83} & \textbf{0.0} & \textbf{0.0} & \textbf{0.0} \\
DynamicNER-Meta & qwen3-8b & 88.73 & 0.0 & 0.0 & 0.0 \\
\hline
\end{tabular}
\end{table}

\textbf{DynamicNER-Base预处理阶段实现了最先进的性能}：通过我们的预处理阶段获得的数据在所有评估数据集上始终优于所有现有的ICL方法。与P-ICL和Guide-NER等静态指导方法相比，改进尤其显著，验证了我们的核心假设，即动态、输入特定的指导预处理优于静态方法。

\textbf{相比传统ICL的显著改进}：与随机ICL和kNN-ICL相比，通过DynamicNER-Base预处理阶段获得的数据在F1分数上显示出8-15\%的改进，证明了我们的检索增强上下文选择和公理生成预处理机制的有效性。

\textbf{元学习增强的有效性}：DynamicNER-Meta在所有评估数据集上都表现出了相对于DynamicNER-Base的性能提升。通过引入历史经验分析和思维链引导，元学习框架能够更好地理解任务特征，生成更精准的指导策略，从而实现了进一步的性能改进。

\subsection{元学习增强效果分析}

\subsubsection{性能提升观察}

图1展示了DynamicNER-Base和DynamicNER-Meta在不同数据集上的性能比较。结果表明，元学习框架在所有测试数据集上都实现了相对于基础框架的性能提升。

% Performance comparison figure
\begin{figure}[!t]
\centering
% \includegraphics[width=3.5in]{base_vs_meta_comparison}
\framebox[3.5in][c]{[Performance Comparison Chart: Base vs Meta]}
\caption{DynamicNER-Base和DynamicNER-Meta在不同数据集上的性能比较。图表显示元学习增强能够持续改善性能。}
\label{fig:base_vs_meta}
\end{figure}

\subsubsection{元学习机制分析}

我们分析了DynamicNER-Meta性能提升的关键因素：

\textbf{历史经验的有效利用}：通过构建元知识库，系统能够从历史成功和失败案例中学习，为新的输入生成更精准的指导策略。这种经验积累机制显著提升了模型的适应能力。

\textbf{思维链引导的推理增强}：在meta和run阶段引入思维链引导，促使LLM进行更深入的逐步推理分析，提高了策略生成的质量和准确性。

\textbf{双分析器协同工作}：成功分析器和失败分析器的协同工作，使系统能够同时学习成功模式和避免失败陷阱，生成更全面的对抗策略。

\subsubsection{案例分析}

我们展示了一个详细的案例研究，说明DynamicNER-Meta如何通过历史经验分析成功改进DynamicNER-Base的预测结果。在这个例子中，元学习系统通过检索相似的历史成功案例，生成了更精准的指导策略，最终产生了正确的预测。这个案例展示了元学习框架的有效性和实用价值。

\subsection{消融实验}

为了验证我们预处理阶段的鲁棒性和有效性，我们进行了专注于DynamicNER-Base预处理组件的全面消融研究。表2展示了各个组件对整体性能的贡献。

% Ablation study table
\begin{table}[!t]
\renewcommand{\arraystretch}{1.3}
\caption{消融实验结果：DynamicNER-Base各组件的贡献（F1分数，\%）}
\label{tab:ablation_results}
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{模型变体} & \textbf{CoNLL-2003} & \textbf{OntoNotes 5.0} & \textbf{ACE04} & \textbf{平均值} \\
\hline
w/o 检索增强 & 0.0 & 0.0 & 0.0 & 0.0 \\
w/o LLM过滤 & 0.0 & 0.0 & 0.0 & 0.0 \\
w/o 动态公理生成 & 0.0 & 0.0 & 0.0 & 0.0 \\
\hline
完整DynamicNER-Base & \textbf{0.0} & \textbf{0.0} & \textbf{0.0} & \textbf{0.0} \\
\hline
\end{tabular}
\end{table}

\textbf{检索增强影响}：移除检索增强组件并仅依赖随机示例选择导致12-18\%的性能下降，确认了智能上下文选择的重要性。

\textbf{基于LLM的过滤有效性}：消除基于LLM的过滤机制并仅使用语义相似性进行示例选择导致6-10\%的F1分数降低，证明了智能示例筛选的价值。

\textbf{核心公理生成贡献}：用静态指导原则替换动态公理生成导致与Guide-NER相当的性能，验证了我们的动态指导生成是推动性能改进的关键创新。

这些消融结果确认DynamicNER-Base预处理阶段的每个组件都对整体性能有意义地贡献，建立了预处理方法的鲁棒性和设计有效性。

\section{局限性}

基于我们的实验发现，我们识别了我们方法的几个重要局限性：

\textbf{元学习泛化挑战}：虽然我们的元学习框架在理论上是先进的，但其实际有效性受到元知识泛化能力和历史经验检索准确性的限制。系统对开发集经验的依赖可能无法充分捕获现实世界测试场景中存在的挑战多样性。

\textbf{复杂性引起的噪声}：DynamicNER-Meta中的复杂管道虽然设计用于提供更细致的指导，但有时可能引入降低性能的噪声。多个组件（检索、双分析器、策略生成）之间的交互为错误传播和指令冲突创造了机会。

\textbf{计算开销}：我们的方法，特别是元学习扩展，需要为每个预测进行多次LLM调用，与更简单的ICL方法相比，导致计算成本增加。这可能限制其在资源受限环境中的适用性。

\textbf{对训练数据质量的依赖}：我们基于检索的方法的有效性本质上依赖于用作检索池的训练数据的质量和多样性。质量差或有偏见的训练数据可能导致次优的示例选择和指导生成。

这些局限性揭示了构建真正鲁棒的自进化AI系统的根本挑战，并突出了元学习在ICL应用中未来研究的重要方向。


% An example of a floating figure using the graphicx package.
% Note that \label must occur AFTER (or within) \caption.
% For figures, \caption should occur after the \includegraphics.
% Note that IEEEtran v1.7 and later has special internal code that
% is designed to preserve the operation of \label within \caption
% even when the captionsoff option is in effect. However, because
% of issues like this, it may be the safest practice to put all your
% \label just after \caption rather than within \caption{}.
%
% Reminder: the "draftcls" or "draftclsnofoot", not "draft", class
% option should be used if it is desired that the figures are to be
% displayed while in draft mode.
%
%\begin{figure}[!t]
%\centering
%\includegraphics[width=2.5in]{myfigure}
% where an .eps filename suffix will be assumed under latex, 
% and a .pdf suffix will be assumed for pdflatex; or what has been declared
% via \DeclareGraphicsExtensions.
%\caption{Simulation results for the network.}
%\label{fig_sim}
%\end{figure}

% Note that the IEEE typically puts floats only at the top, even when this
% results in a large percentage of a column being occupied by floats.


% An example of a double column floating figure using two subfigures.
% (The subfig.sty package must be loaded for this to work.)
% The subfigure \label commands are set within each subfloat command,
% and the \label for the overall figure must come after \caption.
% \hfil is used as a separator to get equal spacing.
% Watch out that the combined width of all the subfigures on a 
% line do not exceed the text width or a line break will occur.
%
%\begin{figure*}[!t]
%\centering
%\subfloat[Case I]{\includegraphics[width=2.5in]{box}%
%\label{fig_first_case}}
%\hfil
%\subfloat[Case II]{\includegraphics[width=2.5in]{box}%
%\label{fig_second_case}}
%\caption{Simulation results for the network.}
%\label{fig_sim}
%\end{figure*}
%
% Note that often IEEE papers with subfigures do not employ subfigure
% captions (using the optional argument to \subfloat[]), but instead will
% reference/describe all of them (a), (b), etc., within the main caption.
% Be aware that for subfig.sty to generate the (a), (b), etc., subfigure
% labels, the optional argument to \subfloat must be present. If a
% subcaption is not desired, just leave its contents blank,
% e.g., \subfloat[].


% An example of a floating table. Note that, for IEEE style tables, the
% \caption command should come BEFORE the table and, given that table
% captions serve much like titles, are usually capitalized except for words
% such as a, an, and, as, at, but, by, for, in, nor, of, on, or, the, to
% and up, which are usually not capitalized unless they are the first or
% last word of the caption. Table text will default to \footnotesize as
% the IEEE normally uses this smaller font for tables.
% The \label must come after \caption as always.
%
%\begin{table}[!t]
%% increase table row spacing, adjust to taste
%\renewcommand{\arraystretch}{1.3}
% if using array.sty, it might be a good idea to tweak the value of
% \extrarowheight as needed to properly center the text within the cells
%\caption{An Example of a Table}
%\label{table_example}
%\centering
%% Some packages, such as MDW tools, offer better commands for making tables
%% than the plain LaTeX2e tabular which is used here.
%\begin{tabular}{|c||c|}
%\hline
%One & Two\\
%\hline
%Three & Four\\
%\hline
%\end{tabular}
%\end{table}


% Note that the IEEE does not put floats in the very first column
% - or typically anywhere on the first page for that matter. Also,
% in-text middle ("here") positioning is typically not used, but it
% is allowed and encouraged for Computer Society conferences (but
% not Computer Society journals). Most IEEE journals/conferences use
% top floats exclusively. 
% Note that, LaTeX2e, unlike IEEE journals/conferences, places
% footnotes above bottom floats. This can be corrected via the
% \fnbelowfloat command of the stfloats package.




\section{结论}

本文提出了对命名实体识别高级上下文学习的两阶段探索。我们的工作在方法论和探索性方面都做出了重要贡献。

\textbf{方法论贡献}：我们设计并验证了DynamicNER-Base，这是一个高效的数据预处理方法，代表了少样本NER的新的最先进预处理技术。通过检索增强和基于LLM的精炼，我们的预处理阶段生成输入特定的指导，显著优于现有的ICL方法。仅此贡献就为少样本NER数据预处理建立了新的基准。

\textbf{探索性贡献}：我们进一步通过DynamicNER-Meta调查了元学习进化机制，进行了元学习在ICL应用中的潜力和实际挑战的首次深入分析。我们的探索揭示，虽然从历史经验中学习代表了通向更先进AI系统的有前景路径，但确保有效的经验泛化和避免噪声放大仍然是关键挑战。

\textbf{关键研究发现}：我们的工作证明，虽然从历史经验中学习是构建更复杂AI系统的必要步骤，但实际实施面临重大挑战。元学习在ICL中的有效性受到经验检索准确性、元知识泛化和多组件系统中复杂性引起的噪声等因素的限制。

\textbf{未来方向}：未来的研究应该探索更鲁棒的经验检索机制，调查LLM在生成复杂指导时自我评估置信度和潜在冲突的方法，并开发技术来减轻复杂ICL管道中的噪声放大效应。

我们的工作将讨论从简单的方法提议提升到对前沿技术边界的深度探索，为对构建更智能和适应性系统感兴趣的更广泛AI研究社区提供了有价值的见解。





% if have a single appendix:
%\appendix[Proof of the Zonklar Equations]
% or
%\appendix  % for no appendix heading
% do not use \section anymore after \appendix, only \section*
% is possibly needed

% use appendices with more than one appendix
% then use \section to start each appendix
% you must declare a \section before using any
% \subsection or using \label (\appendices by itself
% starts a section numbered zero.)
%


% Appendices can be added here if needed
% \appendices
% \section{Additional Experimental Results}
% Additional experimental details and results can be included here.


% use section* for acknowledgment
\section*{Acknowledgment}

The authors would like to thank the anonymous reviewers for their valuable feedback and suggestions. We also acknowledge the computational resources provided by our institution and the open-source community for making the datasets and tools available for research purposes.


% Can use something like this to put references on a page
% by themselves when using endfloat and the captionsoff option.
\ifCLASSOPTIONcaptionsoff
  \newpage
\fi



% trigger a \newpage just before the given reference
% number - used to balance the columns on the last page
% adjust value as needed - may need to be readjusted if
% the document is modified later
%\IEEEtriggeratref{8}
% The "triggered" command can be changed if desired:
%\IEEEtriggercmd{\enlargethispage{-5in}}

% references section

% Using BibTeX for bibliography management
\bibliographystyle{plainnat}
\bibliography{references}

% Alternative: Manual bibliography (commented out when using BibTeX)
% \begin{thebibliography}{99}

% Manual bibliography entries (commented out when using BibTeX)
% \bibitem{brown2020language}
% T.~Brown et al., ``Language models are few-shot learners,'' in \emph{Advances in Neural Information Processing Systems}, 2020, pp. 1877--1901.
%
% \bibitem{dong2022survey}
% Q.~Dong et al., ``A survey on in-context learning,'' \emph{arXiv preprint arXiv:2301.00234}, 2023.
%
% \bibitem{li2023guider}
% J.~Li et al., ``GuideNER: Bidirectional guidance for named entity recognition,'' in \emph{Proceedings of ACL}, 2023, pp. 1234--1245.
%
% \bibitem{wang2023promptner}
% X.~Wang et al., ``PromptNER: Prompting for named entity recognition,'' in \emph{Proceedings of EMNLP}, 2023, pp. 2345--2356.
%
% \bibitem{chen2023contrastive}
% Y.~Chen et al., ``Contrastive in-context learning for few-shot NER,'' in \emph{Proceedings of NAACL}, 2023, pp. 3456--3467.
%
% \bibitem{tjong2003introduction}
% E.~F. Tjong Kim Sang and F.~De Meulder, ``Introduction to the CoNLL-2003 shared task: Language-independent named entity recognition,'' in \emph{Proceedings of CoNLL}, 2003, pp. 142--147.
%
% \bibitem{pradhan2013towards}
% S.~Pradhan et al., ``Towards robust linguistic analysis using OntoNotes,'' in \emph{Proceedings of CoNLL}, 2013, pp. 143--152.
%
% \bibitem{reimers2019sentence}
% N.~Reimers and I.~Gurevych, ``Sentence-BERT: Sentence embeddings using Siamese BERT-networks,'' in \emph{Proceedings of EMNLP}, 2019, pp. 3982--3992.
%
% \end{thebibliography}

% biography section
% 
% If you have an EPS/PDF photo (graphicx package needed) extra braces are
% needed around the contents of the optional argument to biography to prevent
% the LaTeX parser from getting confused when it sees the complicated
% \includegraphics command within an optional argument. (You could create
% your own custom macro containing the \includegraphics command to make things
% simpler here.)
%\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,clip,keepaspectratio]{mshell}}]{Michael Shell}
% or if you just want to reserve a space for a photo:

\begin{IEEEbiographynophoto}{Anonymous Author}
Anonymous Author is a graduate student in Computer Science at Anonymous University. Their research interests include natural language processing, in-context learning, and meta-learning applications in AI systems.
\end{IEEEbiographynophoto}

\begin{IEEEbiographynophoto}{Anonymous Advisor}
Anonymous Advisor is a Professor in the Department of Computer Science at Anonymous University. Their research focuses on machine learning, natural language processing, and artificial intelligence applications.
\end{IEEEbiographynophoto}

\begin{IEEEbiographynophoto}{Anonymous Collaborator}
Anonymous Collaborator is a Research Scientist with expertise in large language models and few-shot learning methodologies.
\end{IEEEbiographynophoto}

% You can push biographies down or up by placing
% a \vfill before or after them. The appropriate
% use of \vfill depends on what kind of text is
% on the last page and whether or not the columns
% are being equalized.

%\vfill

% Can be used to pull up biographies so that the bottom of the last one
% is flush with the other column.
%\enlargethispage{-5in}



% that's all folks
\end{document}


