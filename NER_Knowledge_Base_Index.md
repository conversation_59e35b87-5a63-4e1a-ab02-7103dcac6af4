# NER知识库索引系统

## 文献索引表

### A-C
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| adiga2024designing | Adiga et al. | 2024 | Designing Informative Metrics for Few-Shot Example Selection | 提示工程与示例选择 > 示例选择策略 | 复杂度匹配策略，ACL 2024接收 |
| ashok2023promptner | Ashok & Lipton | 2023 | PromptNER: Prompting For Named Entity Recognition | 少样本与零样本NER方法 > 基于LLM的NER框架 | 实体类型定义+链式思考，SOTA性能 |
| babych2003improving | Babych & Hartley | 2003 | Improving machine translation quality with automatic named entity recognition | 综合性研究与工具 > 应用扩展 | 机器翻译中的NER应用 |
| bai2025labelguided | Bai et al. | 2025 | Label-Guided In-Context Learning for Named Entity Recognition | 最新研究趋势与发现 > 标签引导的上下文学习 | DEER方法，标签引导示例选择 |
| brown2020language | Brown et al. | 2020 | Language Models are Few-Shot Learners | 大语言模型与上下文学习 > 基础理论 | ICL概念奠基 |
| chen2023learning | Chen et al. | 2023 | Learning In-context Learning for Named Entity Recognition | 元学习与多智能体方法 > 元学习框架 | 元函数预训练，ACL 2023接收 |
| chen2023selficl | Chen et al. | 2023 | Self-ICL: Zero-Shot In-Context Learning with Self-Generated Demonstrations | 少样本与零样本NER方法 > 零样本方法 | 自生成示例，EMNLP 2023接收 |

### D-H
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| huang2024guidener | Huang et al. | 2024 | GuideNER: Annotation Guidelines Are Better than Examples | 提示工程与示例选择 > 特殊提示技术 | 标注指南优于示例，AAAI 2025接收 |

### I-L
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| jiang2024picl | Jiang et al. | 2024 | P-ICL: Point In-Context Learning for Named Entity Recognition | 提示工程与示例选择 > 特殊提示技术 | 点实体辅助信息，K-Means选择策略 |
| ji2023vicunaner | Ji | 2023 | VicunaNER: Zero/Few-shot Named Entity Recognition using Vicuna | 少样本与零样本NER方法 > 零样本方法 | 开源模型两阶段框架，可复现方案 |
| keraghel2024recent | Keraghel et al. | 2024 | Recent Advances in Named Entity Recognition: A Comprehensive Survey | 综合性研究与工具 > 综述与调研 | 42页详尽分析，深度比较研究 |
| lee2022good | Lee et al. | 2022 | Good Examples Make A Faster Learner | 提示工程与示例选择 > 示例选择策略 | 系统研究示例策略三维度 |
| li2022rethinking | Li et al. | 2022 | Rethinking Negative Sampling for Handling Missing Entity Annotations | 技术挑战与解决方案 > 主要挑战 | 负采样处理漏标实体 |
| liu2021makesgood | Liu et al. | 2021 | What Makes Good In-Context Examples for GPT-3? | 大语言模型与上下文学习 > ICL机制研究 | 早期ICL示例选择研究 |
| lu2022unified | Lu et al. | 2022 | Unified Structure Generation for Universal Information Extraction | 综合性研究与工具 > 统一框架 | UIE统一文本到结构生成框架 |

### M-P
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| meng2019query | Meng et al. | 2019 | Query-Based Named Entity Recognition | 技术挑战与解决方案 > 主要挑战 | 查询式NER，机器阅读理解范式 |
| min2022rethinking | Min et al. | 2022 | Rethinking the Role of Demonstrations: What Makes In-Context Learning Work? | 大语言模型与上下文学习 > ICL机制研究 | 标签正确性影响有限的发现 |
| mo2024cicl | Mo et al. | 2024 | C-ICL: Contrastive In-context Learning for Information Extraction | 提示工程与示例选择 > 对比学习方法 | 对比学习框架，EMNLP 2024接收 |
| molla2006named | Molla et al. | 2006 | Named entity recognition in question answering of speech data | 综合性研究与工具 > 应用扩展 | 问答系统中的NER组件 |
| peng2024metaie | Peng et al. | 2024 | MetaIE: Distilling a Meta Model from LLM | 元学习与多智能体方法 > 元学习框架 | 符号蒸馏构建元模型 |

### Q-T
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| qin2024incontext | Qin et al. | 2024 | In-Context Learning with Iterative Demonstration Selection | 提示工程与示例选择 > 动态示例控制 | 迭代式演示选择(IDS) |
| rodrigues2024meta | Rodrigues & Branco | 2024 | Meta-prompting Optimized Retrieval-augmented Generation | 检索增强与指令微调 > 检索增强生成 | 元提示优化RAG，性能提升30%+ |
| guu2020realm | Guu et al. | 2020 | REALM: Retrieval-Augmented Language Model Pre-Training | 检索增强与指令微调 > 检索增强生成 | 首次提出RAG预训练方法 |

### U-Z
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| villena2024llmner | Villena et al. | 2024 | llmNER: (Zero\|Few)-Shot Named Entity Recognition | 综合性研究与工具 > 实用工具 | Python库，简化提示工程和解析 |
| wang2023gptner | Wang et al. | 2023 | GPT-NER: Named Entity Recognition via Large Language Models | 少样本与零样本NER方法 > 基于LLM的NER框架 | 原文改写格式，实体级kNN检索 |
| wang2023sprompts | Wang et al. | 2023 | S-Prompts Learning with Pre-trained Transformers | 技术挑战与解决方案 > 主要挑战 | 独立跨域提示学习 |
| wang2025cooperative | Wang et al. | 2025 | A Cooperative Multi-Agent Framework for Zero-Shot Named Entity Recognition | 少样本与零样本NER方法 > 多智能体协作 | CMAS四智能体，WWW 2025接收 |
| xie2023empirical | Xie et al. | 2023 | Empirical Study of Zero-Shot NER with ChatGPT | 少样本与零样本NER方法 > 零样本方法 | 分解式问答，句法增强 |
| xie2024retrieval | Xie et al. | 2024 | Retrieval Augmented Instruction Tuning for Open NER | 检索增强与指令微调 > 检索增强生成 | RA-IT框架：检索增强指令微调 |
| zhang2023iner | Zhang et al. | 2023 | 2INER: Instructive and In-Context Learning | 检索增强与指令微调 > 指令微调方法 | 融合指令微调和上下文学习 |
| zhang2024incontext | Zhang et al. | 2024 | In-Context Learning for Few-Shot Nested Named Entity Recognition | 检索增强与指令微调 > 嵌套NER专门方法 | EnDe Retriever创新检索器 |
| zhao2024dynamic | Zhao et al. | 2024 | Dynamic Demonstrations Controller for In-Context Learning | 提示工程与示例选择 > 动态示例控制 | 动态调整示例数量 |

## 方法索引表

### 核心方法分类
| 方法名称 | 类型 | 主要特点 | 适用场景 | 性能特点 |
|----------|------|----------|----------|----------|
| GPT-NER | 生成范式 | 原文改写格式，@@##标记 | 少样本NER | 首次达到全监督性能 |
| PromptNER | 提示工程 | 明确类型定义，链式思考 | 需要详细指导的场景 | 4-9%绝对提升 |
| VicunaNER | 零样本 | 两阶段识别+再识别 | 开源模型零样本 | 解决数据泄露问题 |
| C-ICL | 对比学习 | 正负样本结合 | 需要边界学习的任务 | 显著性能改进 |
| GuideNER | 指南生成 | 标注指南优于示例 | 规则明确的任务 | 12.63个F1点提升 |
| P-ICL | 点实体 | K-Means聚类选择代表性实体词 | 信息密度要求高的场景 | 更高效的示例利用 |
| IDS | 迭代选择 | Zero-shot-CoT引导的动态选择 | 复杂推理任务 | 结合多样性和相似性 |
| D2Controller | 数量控制 | 动态调整示例数量 | 资源受限场景 | 平均4.6%相对提升 |
| CMAS | 多智能体 | 四智能体分工协作 | 复杂零样本任务 | 自反思质量控制 |
| MetaIE | 元学习 | 符号蒸馏小型元模型 | 需要轻量化部署 | 13个数据集SOTA |
| RA-IT | 检索增强 | 检索增强指令微调 | 开放域NER | 中英文双语验证 |
| EnDe Retriever | 嵌套NER | 三维相似性对比学习 | 嵌套实体场景 | 专门优化边界识别 |
| Self-ICL | 零样本 | 自生成示例 | 无标注数据场景 | 解决示例缺乏问题 |
| REALM | 检索增强 | 预训练阶段整合检索 | 知识密集型任务 | RAG方法基础 |

## 技术概念索引

### 核心技术概念
| 概念 | 定义 | 相关方法 | 应用价值 |
|------|------|----------|----------|
| 上下文学习(ICL) | 通过示例在上下文中学习任务 | GPT-NER, PromptNER等 | 免微调快速适应 |
| 元函数 | λ(instruction, demonstrations, text) → M1 | Chen et al. | 显式ICL能力建模 |
| 原文改写 | 用@@##标记的生成格式 | GPT-NER | 降低生成难度 |
| 硬负样本 | 看起来正确但实际错误的样本 | C-ICL | 增强边界学习 |
| 点实体 | 代表性实体词而非完整句子 | P-ICL | 提高信息密度 |
| 类型相关特征(TRF) | 与实体类型强相关的上下文特征 | CMAS | 任务分解优化 |
| 符号蒸馏 | 从LLM蒸馏元理解能力 | MetaIE | 轻量化部署 |
| 三维相似性 | 语义+边界+标签的综合相似性 | EnDe Retriever | 嵌套NER优化 |
| 迭代示例选择 | 基于推理路径的动态选择 | IDS | 自适应优化 |
| 检索增强指令微调 | 为训练样本检索相似上下文 | RA-IT | 开放域适应 |
| 自生成示例 | 模型自动生成演示样本 | Self-ICL | 零样本学习 |
| 检索增强预训练 | 预训练阶段整合检索机制 | REALM | 知识密集型任务 |

## 最新研究方法索引 (2024-2025)

### 新兴方法
| 方法名称 | 类型 | 主要特点 | 核心创新 | 应用场景 |
|----------|------|----------|----------|----------|
| GistScore | 表示学习 | 任务特定的示例选择表示 | 学习ICL专用嵌入空间 | 通用ICL任务 |
| HiTEC-ICL | 工具学习 | 分层工具选择与ICL结合 | 多工具场景的示例选择 | 复杂工具学习任务 |
| TABGEN-ICL | 结构化生成 | 残差感知示例选择 | 表格数据生成优化 | 结构化数据生成 |
| HiBench | 评估框架 | 多维度分层推理评估 | 层次结构理解评估 | 分层推理任务 |
| DEER | 标签引导 | 利用标签信息指导示例选择 | 标签分布匹配优化 | NER任务专门优化 |

### 理论进展
| 理论发现 | 来源 | 核心观点 | 实践意义 |
|----------|------|----------|----------|
| CoT有效性边界 | ICML 2024 Workshop | CoT在高质量示例下边际贡献有限 | 优先投资示例质量而非复杂推理 |
| 任务特定表示 | GistScore研究 | 通用嵌入不如任务特定表示 | 为不同任务学习专门的选择器 |
| 多维度评估重要性 | HiBench等 | 需要更全面的评估体系 | 建立任务特定的评估基准 |

## 更新日志

### 2025-01-22 重大更新
- 新增2024-2025年最新研究趋势分析
- 添加思维链有效性边界的理论发现
- 更新多维度示例选择的最新进展
- 新增跨领域应用扩展内容
- 建立新兴方法索引表

### 2024-12-28 初始版本
- 基于references.bib创建完整知识库
- 包含所有主要方法的详细分析
- 建立核心设计思想体系
- 创建完整索引系统

### 待更新项目
- [ ] 新增文献的自动索引更新
- [ ] 方法间关系图谱
- [ ] 性能对比表格
- [ ] 代码实现链接
- [ ] 数据集适用性分析
- [x] 2024-2025年最新研究趋势
- [x] 思维链有效性理论分析
- [x] 多维度选择方法进展

## 使用说明

1. **查找特定方法**：使用文献索引表按作者或年份查找
2. **了解技术类型**：使用方法索引表按类型筛选
3. **学习核心概念**：使用技术概念索引理解关键术语
4. **跟踪更新**：查看更新日志了解最新变化

## 维护指南

### 添加新文献时：
1. 在文献索引表中添加新条目
2. 在方法索引表中添加新方法（如适用）
3. 在技术概念索引中添加新概念（如适用）
4. 更新主知识库文件
5. 记录更新日志
