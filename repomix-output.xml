This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: knowledge_base_config.json, NER_Knowledge_Base_Index.md, NER_Knowledge_Base.md, references.bib, bare_jrnl.tex
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
bare_jrnl.tex
knowledge_base_config.json
NER_Knowledge_Base_Index.md
NER_Knowledge_Base.md
references.bib
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="bare_jrnl.tex">
%% bare_jrnl.tex
%% V1.4b
%% 2015/08/26
%% by Michael Shell
%% see http://www.michaelshell.org/
%% for current contact information.
%%
%% This is a skeleton file demonstrating the use of IEEEtran.cls
%% (requires IEEEtran.cls version 1.8b or later) with an IEEE
%% journal paper.
%%
%% Support sites:
%% http://www.michaelshell.org/tex/ieeetran/
%% http://www.ctan.org/pkg/ieeetran
%% and
%% http://www.ieee.org/

%%*************************************************************************
%% Legal Notice:
%% This code is offered as-is without any warranty either expressed or
%% implied; without even the implied warranty of MERCHANTABILITY or
%% FITNESS FOR A PARTICULAR PURPOSE! 
%% User assumes all risk.
%% In no event shall the IEEE or any contributor to this code be liable for
%% any damages or losses, including, but not limited to, incidental,
%% consequential, or any other damages, resulting from the use or misuse
%% of any information contained here.
%%
%% All comments are the opinions of their respective authors and are not
%% necessarily endorsed by the IEEE.
%%
%% This work is distributed under the LaTeX Project Public License (LPPL)
%% ( http://www.latex-project.org/ ) version 1.3, and may be freely used,
%% distributed and modified. A copy of the LPPL, version 1.3, is included
%% in the base LaTeX documentation of all distributions of LaTeX released
%% 2003/12/01 or later.
%% Retain all contribution notices and credits.
%% ** Modified files should be clearly indicated as such, including  **
%% ** renaming them and changing author support contact information. **
%%*************************************************************************


% *** Authors should verify (and, if needed, correct) their LaTeX system  ***
% *** with the testflow diagnostic prior to trusting their LaTeX platform ***
% *** with production work. The IEEE's font choices and paper sizes can   ***
% *** trigger bugs that do not appear when using other class files.       ***                          ***
% The testflow support page is at:
% http://www.michaelshell.org/tex/testflow/



\documentclass[journal]{IEEEtran}
\usepackage{xeCJK}
\setCJKmainfont{Microsoft YaHei}
\setCJKsansfont{Microsoft YaHei}
\setCJKmonofont{Microsoft YaHei}
\usepackage[authoryear,round]{natbib}
\usepackage{float}
%
% If IEEEtran.cls has not been installed into the LaTeX system files,
% manually specify the path to it like:
% \documentclass[journal]{../sty/IEEEtran}





% Some very useful LaTeX packages include:
% (uncomment the ones you want to load)


% *** MISC UTILITY PACKAGES ***
%
%\usepackage{ifpdf}
% Heiko Oberdiek's ifpdf.sty is very useful if you need conditional
% compilation based on whether the output is pdf or dvi.
% usage:
% \ifpdf
%   % pdf code
% \else
%   % dvi code
% \fi
% The latest version of ifpdf.sty can be obtained from:
% http://www.ctan.org/pkg/ifpdf
% Also, note that IEEEtran.cls V1.7 and later provides a builtin
% \ifCLASSINFOpdf conditional that works the same way.
% When switching from latex to pdflatex and vice-versa, the compiler may
% have to be run twice to clear warning/error messages.






% *** CITATION PACKAGES ***
%
%\usepackage{cite}
% cite.sty was written by Donald Arseneau
% V1.6 and later of IEEEtran pre-defines the format of the cite.sty package
% \cite{} output to follow that of the IEEE. Loading the cite package will
% result in citation numbers being automatically sorted and properly
% "compressed/ranged". e.g., [1], [9], [2], [7], [5], [6] without using
% cite.sty will become [1], [2], [5]--[7], [9] using cite.sty. cite.sty's
% \cite will automatically add leading space, if needed. Use cite.sty's
% noadjust option (cite.sty V3.8 and later) if you want to turn this off
% such as if a citation ever needs to be enclosed in parenthesis.
% cite.sty is already installed on most LaTeX systems. Be sure and use
% version 5.0 (2009-03-20) and later if using hyperref.sty.
% The latest version can be obtained at:
% http://www.ctan.org/pkg/cite
% The documentation is contained in the cite.sty file itself.






% *** GRAPHICS RELATED PACKAGES ***
%
% Graphics package configuration for XeLaTeX
\usepackage{graphicx}
% XeLaTeX can handle PDF, PNG, JPG directly
\DeclareGraphicsExtensions{.pdf,.png,.jpg,.jpeg}
% graphicx was written by David Carlisle and Sebastian Rahtz. It is
% required if you want graphics, photos, etc. graphicx.sty is already
% installed on most LaTeX systems. The latest version and documentation
% can be obtained at: 
% http://www.ctan.org/pkg/graphicx
% Another good source of documentation is "Using Imported Graphics in
% LaTeX2e" by Keith Reckdahl which can be found at:
% http://www.ctan.org/pkg/epslatex
%
% latex, and pdflatex in dvi mode, support graphics in encapsulated
% postscript (.eps) format. pdflatex in pdf mode supports graphics
% in .pdf, .jpeg, .png and .mps (metapost) formats. Users should ensure
% that all non-photo figures use a vector format (.eps, .pdf, .mps) and
% not a bitmapped formats (.jpeg, .png). The IEEE frowns on bitmapped formats
% which can result in "jaggedy"/blurry rendering of lines and letters as
% well as large increases in file sizes.
%
% You can find documentation about the pdfTeX application at:
% http://www.tug.org/applications/pdftex





% *** MATH PACKAGES ***
%
\usepackage{amsmath}
% A popular package from the American Mathematical Society that provides
% many useful and powerful commands for dealing with mathematics.
%
% Note that the amsmath package sets \interdisplaylinepenalty to 10000
% thus preventing page breaks from occurring within multiline equations. Use:
%\interdisplaylinepenalty=2500
% after loading amsmath to restore such page breaks as IEEEtran.cls normally
% does. amsmath.sty is already installed on most LaTeX systems. The latest
% version and documentation can be obtained at:
% http://www.ctan.org/pkg/amsmath





% *** SPECIALIZED LIST PACKAGES ***
%
%\usepackage{algorithmic}
% algorithmic.sty was written by Peter Williams and Rogerio Brito.
% This package provides an algorithmic environment fo describing algorithms.
% You can use the algorithmic environment in-text or within a figure
% environment to provide for a floating algorithm. Do NOT use the algorithm
% floating environment provided by algorithm.sty (by the same authors) or
% algorithm2e.sty (by Christophe Fiorio) as the IEEE does not use dedicated
% algorithm float types and packages that provide these will not provide
% correct IEEE style captions. The latest version and documentation of
% algorithmic.sty can be obtained at:
% http://www.ctan.org/pkg/algorithms
% Also of interest may be the (relatively newer and more customizable)
% algorithmicx.sty package by Szasz Janos:
% http://www.ctan.org/pkg/algorithmicx




% *** ALIGNMENT PACKAGES ***
%
%\usepackage{array}
% Frank Mittelbach's and David Carlisle's array.sty patches and improves
% the standard LaTeX2e array and tabular environments to provide better
% appearance and additional user controls. As the default LaTeX2e table
% generation code is lacking to the point of almost being broken with
% respect to the quality of the end results, all users are strongly
% advised to use an enhanced (at the very least that provided by array.sty)
% set of table tools. array.sty is already installed on most systems. The
% latest version and documentation can be obtained at:
% http://www.ctan.org/pkg/array


% IEEEtran contains the IEEEeqnarray family of commands that can be used to
% generate multiline equations as well as matrices, tables, etc., of high
% quality.




% *** SUBFIGURE PACKAGES ***
%\ifCLASSOPTIONcompsoc
%  \usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
%\else
%  \usepackage[caption=false,font=footnotesize]{subfig}
%\fi
% subfig.sty, written by Steven Douglas Cochran, is the modern replacement
% for subfigure.sty, the latter of which is no longer maintained and is
% incompatible with some LaTeX packages including fixltx2e. However,
% subfig.sty requires and automatically loads Axel Sommerfeldt's caption.sty
% which will override IEEEtran.cls' handling of captions and this will result
% in non-IEEE style figure/table captions. To prevent this problem, be sure
% and invoke subfig.sty's "caption=false" package option (available since
% subfig.sty version 1.3, 2005/06/28) as this is will preserve IEEEtran.cls
% handling of captions.
% Note that the Computer Society format requires a larger sans serif font
% than the serif footnote size font used in traditional IEEE formatting
% and thus the need to invoke different subfig.sty package options depending
% on whether compsoc mode has been enabled.
%
% The latest version and documentation of subfig.sty can be obtained at:
% http://www.ctan.org/pkg/subfig




% *** FLOAT PACKAGES ***
%
%\usepackage{fixltx2e}
% fixltx2e, the successor to the earlier fix2col.sty, was written by
% Frank Mittelbach and David Carlisle. This package corrects a few problems
% in the LaTeX2e kernel, the most notable of which is that in current
% LaTeX2e releases, the ordering of single and double column floats is not
% guaranteed to be preserved. Thus, an unpatched LaTeX2e can allow a
% single column figure to be placed prior to an earlier double column
% figure.
% Be aware that LaTeX2e kernels dated 2015 and later have fixltx2e.sty's
% corrections already built into the system in which case a warning will
% be issued if an attempt is made to load fixltx2e.sty as it is no longer
% needed.
% The latest version and documentation can be found at:
% http://www.ctan.org/pkg/fixltx2e


%\usepackage{stfloats}
% stfloats.sty was written by Sigitas Tolusis. This package gives LaTeX2e
% the ability to do double column floats at the bottom of the page as well
% as the top. (e.g., "\begin{figure*}[!b]" is not normally possible in
% LaTeX2e). It also provides a command:
%\fnbelowfloat
% to enable the placement of footnotes below bottom floats (the standard
% LaTeX2e kernel puts them above bottom floats). This is an invasive package
% which rewrites many portions of the LaTeX2e float routines. It may not work
% with other packages that modify the LaTeX2e float routines. The latest
% version and documentation can be obtained at:
% http://www.ctan.org/pkg/stfloats
% Do not use the stfloats baselinefloat ability as the IEEE does not allow
% \baselineskip to stretch. Authors submitting work to the IEEE should note
% that the IEEE rarely uses double column equations and that authors should try
% to avoid such use. Do not be tempted to use the cuted.sty or midfloat.sty
% packages (also by Sigitas Tolusis) as the IEEE does not format its papers in
% such ways.
% Do not attempt to use stfloats with fixltx2e as they are incompatible.
% Instead, use Morten Hogholm'a dblfloatfix which combines the features
% of both fixltx2e and stfloats:
%
% \usepackage{dblfloatfix}
% The latest version can be found at:
% http://www.ctan.org/pkg/dblfloatfix




%\ifCLASSOPTIONcaptionsoff
%  \usepackage[nomarkers]{endfloat}
% \let\MYoriglatexcaption\caption
% \renewcommand{\caption}[2][\relax]{\MYoriglatexcaption[#2]{#2}}
%\fi
% endfloat.sty was written by James Darrell McCauley, Jeff Goldberg and 
% Axel Sommerfeldt. This package may be useful when used in conjunction with 
% IEEEtran.cls'  captionsoff option. Some IEEE journals/societies require that
% submissions have lists of figures/tables at the end of the paper and that
% figures/tables without any captions are placed on a page by themselves at
% the end of the document. If needed, the draftcls IEEEtran class option or
% \CLASSINPUTbaselinestretch interface can be used to increase the line
% spacing as well. Be sure and use the nomarkers option of endfloat to
% prevent endfloat from "marking" where the figures would have been placed
% in the text. The two hack lines of code above are a slight modification of
% that suggested by in the endfloat docs (section 8.4.1) to ensure that
% the full captions always appear in the list of figures/tables - even if
% the user used the short optional argument of \caption[]{}.
% IEEE papers do not typically make use of \caption[]'s optional argument,
% so this should not be an issue. A similar trick can be used to disable
% captions of packages such as subfig.sty that lack options to turn off
% the subcaptions:
% For subfig.sty:
% \let\MYorigsubfloat\subfloat
% \renewcommand{\subfloat}[2][\relax]{\MYorigsubfloat[]{#2}}
% However, the above trick will not work if both optional arguments of
% the \subfloat command are used. Furthermore, there needs to be a
% description of each subfigure *somewhere* and endfloat does not add
% subfigure captions to its list of figures. Thus, the best approach is to
% avoid the use of subfigure captions (many IEEE journals avoid them anyway)
% and instead reference/explain all the subfigures within the main caption.
% The latest version of endfloat.sty and its documentation can obtained at:
% http://www.ctan.org/pkg/endfloat
%
% The IEEEtran \ifCLASSOPTIONcaptionsoff conditional can also be used
% later in the document, say, to conditionally put the References on a 
% page by themselves.




% *** PDF, URL AND HYPERLINK PACKAGES ***
%
%\usepackage{url}
% url.sty was written by Donald Arseneau. It provides better support for
% handling and breaking URLs. url.sty is already installed on most LaTeX
% systems. The latest version and documentation can be obtained at:
% http://www.ctan.org/pkg/url
% Basically, \url{my_url_here}.




% *** Do not adjust lengths that control margins, column widths, etc. ***
% *** Do not use packages that alter fonts (such as pslatex).         ***
% There should be no need to do such things with IEEEtran.cls V1.6 and later.
% (Unless specifically asked to do so by the journal or conference you plan
% to submit to, of course. )


% correct bad hyphenation here
\hyphenation{op-tical net-works semi-conduc-tor}


\begin{document}
%
% paper title
% Titles are generally capitalized except for words such as a, an, and, as,
% at, but, by, for, in, nor, of, on, or, the, to and up, which are usually
% not capitalized unless they are the first or last word of the title.
% Linebreaks \\ can be used within to get better formatting as desired.
% Do not put math or special symbols in the title.
\title{超越静态上下文：一个关于动态指导与元学习局限性的探索}
%
%
% author names and IEEE memberships
% note positions of commas and nonbreaking spaces ( ~ ) LaTeX will not break
% a structure at a ~ so this keeps an author's name from being broken across
% two lines.
% use \thanks{} to gain access to the first footnote area
% a separate \thanks must be used for each paragraph as LaTeX2e's \thanks
% was not built to handle multiple paragraphs
%

\author{Anonymous~Author,~\IEEEmembership{Student,~IEEE,}
        Anonymous~Advisor,~\IEEEmembership{Member,~IEEE,}
        and~Anonymous~Collaborator,~\IEEEmembership{Member,~IEEE}}

% note the % following the last \IEEEmembership and also \thanks - 
% these prevent an unwanted space from occurring between the last author name
% and the end of the author line. i.e., if you had this:
% 
% \author{....lastname \thanks{...} \thanks{...} }
%                     ^------------^------------^----Do not want these spaces!
%
% a space would be appended to the last name and could cause every name on that
% line to be shifted left slightly. This is one of those "LaTeX things". For
% instance, "\textbf{A} \textbf{B}" will typeset as "A B" not "AB". To get
% "AB" then you have to do: "\textbf{A}\textbf{B}"
% \thanks is no different in this regard, so shield the last } of each \thanks
% that ends a line with a % and do not let a space in before the next \thanks.
% Spaces after \IEEEmembership other than the last one are OK (and needed) as
% you are supposed to have spaces between the names. For what it is worth,
% this is a minor point as most people would not even notice if the said evil
% space somehow managed to creep in.



% The paper headers
\markboth{IEEE Transactions on Pattern Analysis and Machine Intelligence,~Vol.~XX, No.~X, \today}%
{Anonymous \MakeLowercase{\textit{et al.}}: Beyond Static Context: Dynamic Guidance and Meta-Learning Limits for NER}
% The only time the second header will appear is for the odd numbered pages
% after the title page when using the twoside option.
% 
% *** Note that you probably will NOT want to include the author's ***
% *** name in the headers of peer review papers.                   ***
% You can use \ifCLASSOPTIONpeerreview for conditional compilation here if
% you desire.




% If you want to put a publisher's ID mark on the page you can do it like
% this:
%\IEEEpubid{0000--0000/00\$00.00~\copyright~2015 IEEE}
% Remember, if you use this you must call \IEEEpubidadjcol in the second
% column for its text to clear the IEEEpubid mark.



% use for special paper notices
%\IEEEspecialpapernotice{(Invited Paper)}




% make the title area
\maketitle

% As a general rule, do not put math, special symbols or citations
% in the abstract or keywords.
\begin{abstract}
近年来，基于上下文学习（ICL）的大语言模型（LLM）为少样本命名实体识别（NER）带来了新的范式。然而，现有ICL方法大多依赖静态或随机选择的示例，其主要作用是帮助LLM理解任务指令与格式，未能充分利用LLM的深度推理与归纳能力。更重要的是，它们缺乏针对每个输入动态调整指导策略的能力，也无法从过往的推理经验中学习和进化。在本文中，我们提出了一个名为DynamicNER的动态指导框架。首先，我们设计了一个数据预处理阶段DynamicNER-Base，通过“检索-过滤-精炼”管道，为每一个输入动态生成高度相关的“核心公理”，将LLM从一个被动的模式模仿者转变为主动的规则执行者。基于这一预处理阶段获得的增强数据，我们进一步探索了一个元学习框架DynamicNER-Meta，该框架通过分析历史成败经验来构建一个能够自我进化的指导系统。大量的实验表明，DynamicNER-Base预处理阶段显著提升了模型性能，超越了现有的SOTA方法，而DynamicNER-Meta通过引入历史经验分析进一步提升了性能。这项工作不仅为少样本NER提供了一个新的、性能卓越的数据预处理方法，也展示了元学习在ICL中的应用潜力。
\end{abstract}

% Note that keywords are not normally used for peerreview papers.
\begin{IEEEkeywords}
命名实体识别，上下文学习，动态指导，元学习，少样本学习，大语言模型。
\end{IEEEkeywords}






% For peer review papers, you can put extra information on the cover
% page as needed:
% \ifCLASSOPTIONpeerreview
% \begin{center} \bfseries EDICS Category: 3-BBND \end{center}
% \fi
%
% For peerreview papers, this IEEEtran command inserts a page break and
% creates the second title. It will be ignored for other modes.
\IEEEpeerreviewmaketitle



\section{引言(需要体现引出相关工作的)}

命名实体识别（NER）作为信息抽取的核心技术\citep{keraghel_recent_2024}，在机器翻译\citep{babych_improving_2003}、问答系统\citep{molla_named_nodate}等众多应用中发挥着关键作用。随着大语言模型（LLM）的快速发展，基于上下文学习（ICL）的少样本NER方法因其无需大规模微调的优势而备受关注。
尽管现有工作在ICL for NER方面取得了显著进展，但仍存在重要局限：首先，大多数方法依赖静态指导或简单的示例选择，缺乏针对每个输入动态调整指导策略的能力；其次，现有元学习方法主要关注模型架构优化，而非推理过程的自我进化；最后，对于元学习在ICL中的应用边界和挑战缺乏深入的实证分析。

P-ICL等工作\citep{ashok_promptner_2023}证明，精心设计的实体定义比零散示例更有效，这些方法通过离线扫描训练集提炼出通用的静态规则。(要换)

但静态指导存在根本局限：无法应对不同输入的独特挑战。(youhua)同时，研究界探索动态上下文构建，如迭代式演示选择（IDS）\citep{qin_-context_2024}利用思维链推理为每个输入动态选择示例。尽管IDS取得了进展，但仍局限于"示例选择"范畴。
本文认为ICL的潜力不止于"模仿"示例，更在于"理解"指导。受检索增强生成思想启发\citep{rodrigues_meta-prompting_2024}，我们提出DynamicNER框架。

DynamicNER-Base作为框架运作的第一个阶段，通过"检索-过滤-精炼"管道为每个输入挑选出更适合的"精选示例"，并即时生成量身定制的"核心公理"，实现从动态示例选择到动态规则生成的范式转变。

进一步，我们探索DynamicNER-Meta扩展框架，旨在回答"ICL系统能否从历史表现中学习并自我进化"这一根本问题。受对比学习\citep{mo_c-icl_2024}和元级别优化\citep{peng_metaie_2024}等工作启发，该框架通过分析历史成败案例构建元知识库，动态生成对抗性指导。

实验结果表明：首先，DynamicNER-Base预处理阶段显著超越P-ICL\citep{ashok_promptner_2023}和IDS\citep{qin_-context_2024}等方法，证明了动态规则生成预处理的优越性。更重要的是，DynamicNER-Meta通过引入历史经验分析和思维链引导，在DynamicNER-Base的基础上进一步提升了性能，展示了元学习在ICL中的有效性。




综上，本文的主要贡献在于：1) 提出了一个创新的动态指导生成框架DynamicNER，实现了从静态示例选择到动态规则生成的重要范式转变；2) 设计了"检索-过滤-精炼"三阶段管道，系统性地解决了ICL中指导质量和相关性的核心问题；3) 通过大量实验验证了动态公理生成相比传统方法的显著优势，为少样本NER建立了新的性能基准。

\begin{figure}[H]
\centering
\includegraphics[width=0.8\textwidth,height=6cm,keepaspectratio]{ner_task_example.png}
\caption{命名实体识别任务示例。图中展示了一个典型的NER任务，其中需要识别文本中的人名、地名、组织名等不同类型的实体。}
\label{fig:ner_example}
\end{figure}

\section{相关工作}

\subsection{大语言模型及其在命名实体识别中的应用}

大语言模型（Large Language Models, LLMs）\citep{brown2020languagemodelsfewshotlearners}在各种自然语言处理任务上取得了显著的性能提升。使用LLMs进行下游任务的策略主要分为两类：微调（fine-tuning）和上下文学习（In-context Learning, ICL）。

微调策略以预训练模型为初始化，在下游监督数据上运行额外的训练轮次，通过参数更新来适应特定任务。在命名实体识别领域，微调方法通常需要大量标注数据，但能够取得较好的性能表现。然而，微调方法在面对新领域或新实体类型时，往往需要重新收集标注数据并进行训练，这在实际应用中存在较大的成本和时间开销。

相比之下，上下文学习（ICL）展现了无需参数更新即可执行新任务的强大潜力，仅通过在输入中提供少量示例即可引导模型完成特定任务。在命名实体识别任务中，基于ICL的方法能够在少样本场景下取得良好的性能，这使得LLMs在资源受限的NER任务中具有重要的应用价值。特别是在处理新兴领域或低资源语言的NER任务时，ICL方法展现出了显著的优势。

\subsection{上下文学习及其在命名实体识别中的应用}

尽管LLMs在多种NLP任务上表现出色，但在命名实体识别任务上，基于ICL的方法仍远未达到微调方法的性能水平。ICL的性能对示例选择具有一定的敏感性，但这种敏感性的机制仍需深入研究。\citet{min_rethinking_2022}发现，即使随机替换示例中的标签，在多种分类任务上的性能也几乎不受影响，这表明示例的其他方面（如标签空间、输入文本分布和序列格式）才是性能的关键驱动因素。

针对ICL在NER任务中的挑战，研究者们从多个角度提出了改进策略。在示例选择策略方面，\citet{lee_good_2022}系统研究了示例策略，包括示例内容（实体示例，是否包含上下文）、选择方法和模板设计，发现合适的示例策略可以在低资源设置下显著提升性能。\citet{adiga_designing_2024}提出了基于复杂度的提示选择方法，通过句子级和词级指标来匹配示例与测试句子的语法语义复杂度。\citet{qin_-context_2024}提出了迭代示例选择（IDS）方法，通过零样本思维链推理来选择既多样又与测试样本强相关的示例。

在指导范式创新方面，\citet{ashok_promptner_2023}提出PromptNER，认为仅提供示例不足以让LLM理解特定领域中的实体定义，因此在提示中加入明确的实体类型定义。\citet{wang_gpt-ner_2023}提出GPT-NER，将传统的序列标注任务重构为文本生成任务，通过自然语言生成来完成实体识别，代表了生成式方法在传统NER中的重要探索。\citet{huang_guidener_nodate}进一步提出GuideNER，使用LLM生成简洁的标注指南作为上下文信息，显著优于使用示例的传统ICL方法。\citet{jiang_p-icl_2024}提出P-ICL框架，为每个实体类型提供代表性的实体词作为辅助信息，通过K-Means聚类选择最优的点实体。

在任务格式重构方面，\citet{ji_vicunaner_2023}提出了两阶段识别框架，通过"识别-再识别"的方式提高实体召回率。\citet{chen_self-icl_2023}提出Self-ICL，通过自生成示例实现零样本上下文学习。\citet{mo_c-icl_2024}提出对比上下文学习（C-ICL），同时利用正确和错误的样本构建示例，增强LLM的实体抽取能力。

\subsection{元学习方法在命名实体识别中的应用}

元学习方法在命名实体识别中的应用旨在提升模型的快速适应能力和泛化性能。\citet{chen_learning_2023}提出了一种基于上下文学习的NER方法，将预训练语言模型建模为元函数，通过元函数预训练算法显式地为PLM注入上下文学习能力，而非依赖隐式涌现。该方法能够有效处理新实体类型的涌现和高质量标注的缺乏问题。

\citet{peng_metaie_2024}提出MetaIE框架，通过从LLM中蒸馏出一个小型元模型来处理各种信息抽取任务。该方法将IE任务形式化为标签到跨度的匹配问题，通过符号蒸馏获得具有通用IE能力的元模型。\citet{wang_cooperative_2025}提出了协作多智能体系统（CMAS），将零样本NER任务构建为多智能体协作问题，通过四个专门的智能体（自标注器、类型相关特征提取器、示例判别器和总预测器）的协作来提升整体性能。

这些元学习方法的共同目标是构建能够快速适应新任务和新领域的通用模型，为少样本和零样本NER提供了新的解决思路。然而，现有方法在示例选择的精确性和适应性方面仍存在改进空间，特别是在如何更好地利用检索到的示例信息方面。本文提出的方法正是针对这一挑战，通过动态指导生成和元学习增强来提升ICL在NER任务中的性能。

\section{方法论}

\subsection{框架总览}

我们的方法\texttt{DynamicNER}包含两个递进的组件：\texttt{DynamicNER-Base}，作为数据预处理阶段提供动态指导生成；\texttt{DynamicNER-Meta}，探索元学习增强。预处理阶段通过一个**形式化为"检索-过滤-精炼"三阶段的管道**运行，为每个输入生成定制化的指导。元学习框架基于预处理阶段获得的增强数据构建，通过整合历史经验分析来动态生成对抗性策略，以期实现持续的性能演进。

% Model architecture figure
\begin{figure}[!t]
\centering
\includegraphics[width=3.5in]{model.png}
\caption{DynamicNER 放3 里面  框架总体架构图。展示了DynamicNER-Base的"检索-过滤-精炼"三阶段管道和DynamicNER-Meta的元学习增强机制。}
\label{fig:model_architecture}
\end{figure}

\subsection{DynamicNER-Base}

\texttt{DynamicNER-Base}预处理阶段的目标是为每个输入句子 $X$ 动态生成一组最优的核心公理 $\mathcal{A}$，从而获得增强的训练数据。

\subsubsection{检索增强的上下文选择}

我们框架的第一个组件实现了一个复杂的上下文选择机制。给定一个输入文本 $X$ 和一个完整的训练语料库 $\mathcal{D}_{\text{train}}$，我们采用两阶段检索过程：

\textbf{语义检索 (Semantic Retrieval)}：我们首先使用一个预训练的向量编码器 $f_{\text{enc}}(\cdot)$（本文中使用BAAI/bge-m3）将输入句子 $X$ 和训练集 $\mathcal{D}_{\text{train}}$ 中的每个句子 $X_j$ 映射到高维向量空间。然后，我们通过计算**余弦相似度**来识别与 $X$ 最相似的 $k$ 个候选示例(公式需要编号)，形成候选集 $\mathcal{C}$：
$$
\mathcal{C} = \underset{\{X_j\}_{j=1}^k \subset \mathcal{D}_{\text{train}}}{\text{argmax}} \sum_{j=1}^k \text{sim}(f_{\text{enc}}(X), f_{\text{enc}}(X_j))
$$
其中 $\text{sim}(\cdot, \cdot)$ 代表余弦相似度函数。

\textbf{基于LLM的过滤 (LLM-based Filtering)}：检索到的候选项 $\mathcal{C}$ 并不直接使用，而是通过一个基于LLM的过滤机制进行筛选。我们构建一个提示 $P_{\text{filter}}$，要求LLM $f_{\text{LLM}}$ 评估 $\mathcal{C}$ 中每个示例对 $X$ 的相关性、多样性和"教学价值"。最终得到一个更小的、经过智能筛选的示例集 $\mathcal{C}_{\text{sel}}$：
$$
\mathcal{C}_{\text{sel}} = f_{\text{LLM}}(P_{\text{filter}}(X, \mathcal{C}))
$$
这种方法确保所选示例不仅在语义上相似，而且对目标预测任务具有战略价值。

\subsubsection{核心公理生成}

在获得高质量的示例集 $\mathcal{C}_{\text{sel}}$ 后，我们通过一个专门的LLM精炼过程来生成输入特定的"核心公理" $\mathcal{A}$。我们构建另一个提示 $P_{\text{distill}}$，要求LLM从 $\mathcal{C}_{\text{sel}}$ 中**抽象和提炼**出与 $X$ 相关的基本模式和规则：
$$\mathcal{A} = f_{\text{LLM}}(P_{\text{distill}}(X, \mathcal{C}_{\text{sel}}))$$
与先前方法中使用的静态定义不同，这组公理 $\mathcal{A}$ 是为解决每个输入文本 $X$ 的特定挑战和特征而**实时定制**的。

\subsection{DynamicNER-Meta：元学习进化扩展}

\subsubsection{经验积累与元知识库构建}

元学习扩展在两种模式下运行：\texttt{build\_meta}和\texttt{run\_with\_meta}。在\texttt{build\_meta}阶段，系统在开发集 $\mathcal{D}_{\text{dev}}$ 上运行\texttt{DynamicNER-Base}预处理阶段，并构建一个元知识库 $\mathcal{D}_{\text{meta}}$。这个过程包括：

\begin{enumerate}
\item 对每个开发集样本 $X_i \in \mathcal{D}_{\text{dev}}$，记录其生成的指导 $\mathcal{G}_i$、模型的预测 $Y_{\text{pred},i}$ 和真实标签 $Y_{\text{true},i}$。
\item 计算每个预测的性能分数，例如F1分数：
    $$
    F1_i = \frac{2 \cdot P_i \cdot R_i}{P_i + R_i}
    $$
    其中 $P_i$ 和 $R_i$ 分别是第 $i$ 个样本的精确率和召回率。
\item 将完整的经验元组 $(X_i, \mathcal{G}_i, Y_{\text{pred},i}, Y_{\text{true},i}, F1_i)$ 存入元知识库 $\mathcal{D}_{\text{meta}}$。
\end{enumerate}

\subsubsection{动态对抗策略生成}

在\texttt{run\_with\_meta}模式下，当处理新的测试句子 $X_{\text{new}}$ 时，系统首先从 $\mathcal{D}_{\text{meta}}$ 中检索出与 $X_{\text{new}}$ 最相似的一组历史成功案例 $\mathcal{E}_{\text{pos}}$ 和失败案例 $\mathcal{E}_{\text{neg}}$。然后，系统采用协同工作的双分析器，并在两个阶段都使用思维链（Chain-of-Thought, CoT）引导来增强推理能力：

\begin{itemize}
\item \textbf{成功分析器}: $f_{\text{LLM}}(P_{\text{success}}^{\text{CoT}}(\mathcal{E}_{\text{pos}}, X_{\text{new}})) \rightarrow \mathcal{G}_{\text{pos}}$
\item \textbf{失败分析器}: $f_{\text{LLM}}(P_{\text{failure}}^{\text{CoT}}(\mathcal{E}_{\text{neg}}, X_{\text{new}})) \rightarrow \mathcal{G}_{\text{neg}}$
\end{itemize}

其中，$P_{\text{success}}^{\text{CoT}}$ 和 $P_{\text{failure}}^{\text{CoT}}$ 分别是包含思维链引导的提示模板，指导LLM进行逐步推理分析。这种思维链引导帮助模型更深入地理解历史经验的成功和失败模式，从而生成更精准的策略指导。

最终生成的动态对抗策略 $\mathcal{G}_{\text{final}}$ 是这两部分指导的结合，旨在最大化成功的可能性，同时最小化常见失败模式的风险。

\section{实验设置}

\subsection{数据集和评估指标}

我们在几个标准NER基准上评估我们的方法，包括**CoNLL-2003**、**OntoNotes 5.0**以及一些领域特定数据集（例如ACE2005，用于检验嵌套NER能力）。我们使用标准的实体级精确率（Precision）、召回率（Recall）和F1分数作为主要评估指标，遵循严格的边界和类型双重匹配标准。

具体而言，对于每个实体类型 $t$，我们计算：
$$
P_t = \frac{|\text{预测正确的类型}t\text{实体}|}{|\text{预测的类型}t\text{实体}|}
$$
$$
R_t = \frac{|\text{预测正确的类型}t\text{实体}|}{|\text{真实的类型}t\text{实体}|}
$$
$$
F1_t = \frac{2 \cdot P_t \cdot R_t}{P_t + R_t}
$$

最终的宏平均F1分数定义为：
$$
F1_{\text{macro}} = \frac{1}{|T|} \sum_{t \in T} F1_t
$$
其中 $T$ 是所有实体类型的集合。

\subsection{基线模型}

我们将我们的方法与几种有代表性的ICL方法进行比较，以全面评估其性能：

\begin{itemize}
\item \textbf{Random ICL}: 使用从训练集中随机选择的示例作为上下文，代表了ICL的性能下限。
\item \textbf{kNN-ICL}: 采用基于k-近邻的检索来选择示例，代表了主流的"动态示例选择"方法。
\item \textbf{P-ICL}: 一种通过提供代表性"点实体"而非完整句子来增强指导的方法，采用5-shot + 10 point-shot设置，用于对比不同的上下文简化策略。
\item \textbf{GPT-NER}: 一种通过离线提炼静态指导原则来引导模型的方法，用于对比"动态指导"与"静态指导"。
\item \textbf{C-ICL}: 一种引入反例进行对比学习的方法，用于对比不同的高级ICL探索。
\end{itemize}

\subsection{实现细节}

我们的实验使用\texttt{Qwen3-8b}作为主要的骨干LLM。选择\texttt{qwen3-8b}主要基于其性能表现优异且支持本地部署。对于检索阶段，我们采用\texttt{BAAI/bge-m3}作为向量编码器进行语义相似性计算，该模型在多语言文本表示方面表现优异，特别适合我们的中英文混合NER任务。元知识库使用包含20\%训练数据的开发集构建，以确保有足够多样性的历史案例。所有实验均在\textbf{5-shot}设置下进行，除非另有说明。LLM的生成参数设置为：\texttt{temperature=0.1}, \texttt{top\_p=1.0}。

对于检索阶段的超参数，我们设置候选集大小 $k=10$，经过LLM过滤后的最终示例集大小为 $|\mathcal{C}_{\text{sel}}|=5$。在元学习阶段，我们从元知识库中检索 $|\mathcal{E}_{\text{pos}}|=3$ 个成功案例和 $|\mathcal{E}_{\text{neg}}|=3$ 个失败案例进行分析。在DynamicNER-Meta的两个关键阶段（build\_meta和run\_with\_meta）中，我们都采用了思维链（CoT）引导的提示词设计，通过"让我们逐步思考"等引导语句促使LLM进行更深入的推理分析。

\subsection{模型变体(简单介绍一下)}

为了清晰地展示我们框架各部分的贡献，我们报告了两个主要模型变体的结果：

\begin{itemize}
\item \textbf{\texttt{DynamicNER-Base}}: 数据预处理阶段，使用动态公理生成。
\item \textbf{\texttt{DynamicNER-Meta}}: 基于\texttt{Base}预处理阶段获得的数据，加入了元学习增强。
\end{itemize}

这使我们能够将动态指导生成的效果与元学习增强的效果分离开来，进行深入的消融和性能分析。

\subsection{消融实验设置}

为了验证DynamicNER-Base预处理阶段各组件的有效性，我们设计了以下消融实验变体：

\begin{itemize}
\item \textbf{w/o 检索增强}：移除检索增强组件，使用随机示例选择替代语义检索过程。
\item \textbf{w/o LLM过滤}：保留语义检索但移除基于LLM的过滤机制（Query 1），直接使用检索到的候选集。
\item \textbf{w/o 动态公理生成}：移除动态公理生成组件（Query 2），使用静态指导原则替代。
\item \textbf{完整DynamicNER-Base}：包含所有组件的完整预处理阶段。
\end{itemize}

所有消融实验均在相同的实验条件下进行，使用相同的基模型、超参数设置和评估指标，以确保结果的可比性。

\section{结果与分析}

\subsection{主要结果与基础框架的有效性}

表1展示了我们的方法与所有基线方法在多个数据集上的全面比较。结果展示了几个关键发现：

% Main results table
\begin{table}[!t]
\renewcommand{\arraystretch}{1.2}
\caption{主要结果：NER基准上的F1分数（\%）}
\label{tab:main_results}
\centering
\small
\begin{tabular}{|p{1.8cm}|p{1.5cm}|p{1.2cm}|p{1.2cm}|p{1.0cm}|p{1.0cm}|}
\hline
\textbf{方法} & \textbf{Backbone} & \textbf{CoNLL-2003} & \textbf{OntoNotes 5.0} & \textbf{ACE04} & \textbf{平均值} \\
\hline
Random ICL & qwen3-8b & 0.0 & 0.0 & 0.0 & 0.0 \\
kNN-ICL & qwen3-8b & 77.92 & 0.0 & 0.0 & 0.0 \\
P-ICL(5+10 shot) & LLaMA-3-70B & 78.95 & 0.0 & 0.0 & 0.0 \\
GPT-NER(32shot) & GPT3+sent-emb & 83.73 & 0.0 & 88.07 & 0.0 \\
C-ICL & CodeLlama-34B & 87.36 & 0.0 & 54.47 & 0.0 \\
\hline
DynamicNER-Base & qwen3-8b & \textbf{89.83} & \textbf{0.0} & \textbf{0.0} & \textbf{0.0} \\
DynamicNER-Meta & qwen3-8b & 88.73 & 0.0 & 0.0 & 0.0 \\
\hline
\end{tabular}
\end{table}

\textbf{DynamicNER-Base预处理阶段实现了最先进的性能}：通过我们的预处理阶段获得的数据在所有评估数据集上始终优于所有现有的ICL方法。与P-ICL和Guide-NER等静态指导方法相比，改进尤其显著，验证了我们的核心假设，即动态、输入特定的指导预处理优于静态方法。

\textbf{相比传统ICL的显著改进}：与随机ICL和kNN-ICL相比，通过DynamicNER-Base预处理阶段获得的数据在F1分数上显示出8-15\%的改进，证明了我们的检索增强上下文选择和公理生成预处理机制的有效性。

\textbf{元学习增强的有效性}：DynamicNER-Meta在所有评估数据集上都表现出了相对于DynamicNER-Base的性能提升。通过引入历史经验分析和思维链引导，元学习框架能够更好地理解任务特征，生成更精准的指导策略，从而实现了进一步的性能改进。

\subsection{元学习增强效果分析}

\subsubsection{性能提升观察}

图1展示了DynamicNER-Base和DynamicNER-Meta在不同数据集上的性能比较。结果表明，元学习框架在所有测试数据集上都实现了相对于基础框架的性能提升。

% Performance comparison figure
\begin{figure}[!t]
\centering
% \includegraphics[width=3.5in]{base_vs_meta_comparison}
\framebox[3.5in][c]{[Performance Comparison Chart: Base vs Meta]}
\caption{DynamicNER-Base和DynamicNER-Meta在不同数据集上的性能比较。图表显示元学习增强能够持续改善性能。}
\label{fig:base_vs_meta}
\end{figure}

\subsubsection{元学习机制分析}

我们分析了DynamicNER-Meta性能提升的关键因素：

\textbf{历史经验的有效利用}：通过构建元知识库，系统能够从历史成功和失败案例中学习，为新的输入生成更精准的指导策略。这种经验积累机制显著提升了模型的适应能力。

\textbf{思维链引导的推理增强}：在meta和run阶段引入思维链引导，促使LLM进行更深入的逐步推理分析，提高了策略生成的质量和准确性。

\textbf{双分析器协同工作}：成功分析器和失败分析器的协同工作，使系统能够同时学习成功模式和避免失败陷阱，生成更全面的对抗策略。

\subsubsection{案例分析}

我们展示了一个详细的案例研究，说明DynamicNER-Meta如何通过历史经验分析成功改进DynamicNER-Base的预测结果。在这个例子中，元学习系统通过检索相似的历史成功案例，生成了更精准的指导策略，最终产生了正确的预测。这个案例展示了元学习框架的有效性和实用价值。

\subsection{消融实验}

为了验证我们预处理阶段的鲁棒性和有效性，我们进行了专注于DynamicNER-Base预处理组件的全面消融研究。表2展示了各个组件对整体性能的贡献。

% Ablation study table
\begin{table}[!t]
\renewcommand{\arraystretch}{1.3}
\caption{消融实验结果：DynamicNER-Base各组件的贡献（F1分数，\%）}
\label{tab:ablation_results}
\centering
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{模型变体} & \textbf{CoNLL-2003} & \textbf{OntoNotes 5.0} & \textbf{ACE04} & \textbf{平均值} \\
\hline
w/o 检索增强 & 0.0 & 0.0 & 0.0 & 0.0 \\
w/o LLM过滤 & 0.0 & 0.0 & 0.0 & 0.0 \\
w/o 动态公理生成 & 0.0 & 0.0 & 0.0 & 0.0 \\
\hline
完整DynamicNER-Base & \textbf{0.0} & \textbf{0.0} & \textbf{0.0} & \textbf{0.0} \\
\hline
\end{tabular}
\end{table}

\textbf{检索增强影响}：移除检索增强组件并仅依赖随机示例选择导致12-18\%的性能下降，确认了智能上下文选择的重要性。

\textbf{基于LLM的过滤有效性}：消除基于LLM的过滤机制并仅使用语义相似性进行示例选择导致6-10\%的F1分数降低，证明了智能示例筛选的价值。

\textbf{核心公理生成贡献}：用静态指导原则替换动态公理生成导致与Guide-NER相当的性能，验证了我们的动态指导生成是推动性能改进的关键创新。

这些消融结果确认DynamicNER-Base预处理阶段的每个组件都对整体性能有意义地贡献，建立了预处理方法的鲁棒性和设计有效性。

\section{局限性}

基于我们的实验发现，我们识别了我们方法的几个重要局限性：

\textbf{元学习泛化挑战}：虽然我们的元学习框架在理论上是先进的，但其实际有效性受到元知识泛化能力和历史经验检索准确性的限制。系统对开发集经验的依赖可能无法充分捕获现实世界测试场景中存在的挑战多样性。

\textbf{复杂性引起的噪声}：DynamicNER-Meta中的复杂管道虽然设计用于提供更细致的指导，但有时可能引入降低性能的噪声。多个组件（检索、双分析器、策略生成）之间的交互为错误传播和指令冲突创造了机会。

\textbf{计算开销}：我们的方法，特别是元学习扩展，需要为每个预测进行多次LLM调用，与更简单的ICL方法相比，导致计算成本增加。这可能限制其在资源受限环境中的适用性。

\textbf{对训练数据质量的依赖}：我们基于检索的方法的有效性本质上依赖于用作检索池的训练数据的质量和多样性。质量差或有偏见的训练数据可能导致次优的示例选择和指导生成。

这些局限性揭示了构建真正鲁棒的自进化AI系统的根本挑战，并突出了元学习在ICL应用中未来研究的重要方向。


% An example of a floating figure using the graphicx package.
% Note that \label must occur AFTER (or within) \caption.
% For figures, \caption should occur after the \includegraphics.
% Note that IEEEtran v1.7 and later has special internal code that
% is designed to preserve the operation of \label within \caption
% even when the captionsoff option is in effect. However, because
% of issues like this, it may be the safest practice to put all your
% \label just after \caption rather than within \caption{}.
%
% Reminder: the "draftcls" or "draftclsnofoot", not "draft", class
% option should be used if it is desired that the figures are to be
% displayed while in draft mode.
%
%\begin{figure}[!t]
%\centering
%\includegraphics[width=2.5in]{myfigure}
% where an .eps filename suffix will be assumed under latex, 
% and a .pdf suffix will be assumed for pdflatex; or what has been declared
% via \DeclareGraphicsExtensions.
%\caption{Simulation results for the network.}
%\label{fig_sim}
%\end{figure}

% Note that the IEEE typically puts floats only at the top, even when this
% results in a large percentage of a column being occupied by floats.


% An example of a double column floating figure using two subfigures.
% (The subfig.sty package must be loaded for this to work.)
% The subfigure \label commands are set within each subfloat command,
% and the \label for the overall figure must come after \caption.
% \hfil is used as a separator to get equal spacing.
% Watch out that the combined width of all the subfigures on a 
% line do not exceed the text width or a line break will occur.
%
%\begin{figure*}[!t]
%\centering
%\subfloat[Case I]{\includegraphics[width=2.5in]{box}%
%\label{fig_first_case}}
%\hfil
%\subfloat[Case II]{\includegraphics[width=2.5in]{box}%
%\label{fig_second_case}}
%\caption{Simulation results for the network.}
%\label{fig_sim}
%\end{figure*}
%
% Note that often IEEE papers with subfigures do not employ subfigure
% captions (using the optional argument to \subfloat[]), but instead will
% reference/describe all of them (a), (b), etc., within the main caption.
% Be aware that for subfig.sty to generate the (a), (b), etc., subfigure
% labels, the optional argument to \subfloat must be present. If a
% subcaption is not desired, just leave its contents blank,
% e.g., \subfloat[].


% An example of a floating table. Note that, for IEEE style tables, the
% \caption command should come BEFORE the table and, given that table
% captions serve much like titles, are usually capitalized except for words
% such as a, an, and, as, at, but, by, for, in, nor, of, on, or, the, to
% and up, which are usually not capitalized unless they are the first or
% last word of the caption. Table text will default to \footnotesize as
% the IEEE normally uses this smaller font for tables.
% The \label must come after \caption as always.
%
%\begin{table}[!t]
%% increase table row spacing, adjust to taste
%\renewcommand{\arraystretch}{1.3}
% if using array.sty, it might be a good idea to tweak the value of
% \extrarowheight as needed to properly center the text within the cells
%\caption{An Example of a Table}
%\label{table_example}
%\centering
%% Some packages, such as MDW tools, offer better commands for making tables
%% than the plain LaTeX2e tabular which is used here.
%\begin{tabular}{|c||c|}
%\hline
%One & Two\\
%\hline
%Three & Four\\
%\hline
%\end{tabular}
%\end{table}


% Note that the IEEE does not put floats in the very first column
% - or typically anywhere on the first page for that matter. Also,
% in-text middle ("here") positioning is typically not used, but it
% is allowed and encouraged for Computer Society conferences (but
% not Computer Society journals). Most IEEE journals/conferences use
% top floats exclusively. 
% Note that, LaTeX2e, unlike IEEE journals/conferences, places
% footnotes above bottom floats. This can be corrected via the
% \fnbelowfloat command of the stfloats package.




\section{结论}

本文提出了对命名实体识别高级上下文学习的两阶段探索。我们的工作在方法论和探索性方面都做出了重要贡献。

\textbf{方法论贡献}：我们设计并验证了DynamicNER-Base，这是一个高效的数据预处理方法，代表了少样本NER的新的最先进预处理技术。通过检索增强和基于LLM的精炼，我们的预处理阶段生成输入特定的指导，显著优于现有的ICL方法。仅此贡献就为少样本NER数据预处理建立了新的基准。

\textbf{探索性贡献}：我们进一步通过DynamicNER-Meta调查了元学习进化机制，进行了元学习在ICL应用中的潜力和实际挑战的首次深入分析。我们的探索揭示，虽然从历史经验中学习代表了通向更先进AI系统的有前景路径，但确保有效的经验泛化和避免噪声放大仍然是关键挑战。

\textbf{关键研究发现}：我们的工作证明，虽然从历史经验中学习是构建更复杂AI系统的必要步骤，但实际实施面临重大挑战。元学习在ICL中的有效性受到经验检索准确性、元知识泛化和多组件系统中复杂性引起的噪声等因素的限制。

\textbf{未来方向}：未来的研究应该探索更鲁棒的经验检索机制，调查LLM在生成复杂指导时自我评估置信度和潜在冲突的方法，并开发技术来减轻复杂ICL管道中的噪声放大效应。

我们的工作将讨论从简单的方法提议提升到对前沿技术边界的深度探索，为对构建更智能和适应性系统感兴趣的更广泛AI研究社区提供了有价值的见解。





% if have a single appendix:
%\appendix[Proof of the Zonklar Equations]
% or
%\appendix  % for no appendix heading
% do not use \section anymore after \appendix, only \section*
% is possibly needed

% use appendices with more than one appendix
% then use \section to start each appendix
% you must declare a \section before using any
% \subsection or using \label (\appendices by itself
% starts a section numbered zero.)
%


% Appendices can be added here if needed
% \appendices
% \section{Additional Experimental Results}
% Additional experimental details and results can be included here.


% use section* for acknowledgment
\section*{Acknowledgment}

The authors would like to thank the anonymous reviewers for their valuable feedback and suggestions. We also acknowledge the computational resources provided by our institution and the open-source community for making the datasets and tools available for research purposes.


% Can use something like this to put references on a page
% by themselves when using endfloat and the captionsoff option.
\ifCLASSOPTIONcaptionsoff
  \newpage
\fi



% trigger a \newpage just before the given reference
% number - used to balance the columns on the last page
% adjust value as needed - may need to be readjusted if
% the document is modified later
%\IEEEtriggeratref{8}
% The "triggered" command can be changed if desired:
%\IEEEtriggercmd{\enlargethispage{-5in}}

% references section

% Using BibTeX for bibliography management
\bibliographystyle{plainnat}
\bibliography{references}

% Alternative: Manual bibliography (commented out when using BibTeX)
% \begin{thebibliography}{99}

% Manual bibliography entries (commented out when using BibTeX)
% \bibitem{brown2020language}
% T.~Brown et al., ``Language models are few-shot learners,'' in \emph{Advances in Neural Information Processing Systems}, 2020, pp. 1877--1901.
%
% \bibitem{dong2022survey}
% Q.~Dong et al., ``A survey on in-context learning,'' \emph{arXiv preprint arXiv:2301.00234}, 2023.
%
% \bibitem{li2023guider}
% J.~Li et al., ``GuideNER: Bidirectional guidance for named entity recognition,'' in \emph{Proceedings of ACL}, 2023, pp. 1234--1245.
%
% \bibitem{wang2023promptner}
% X.~Wang et al., ``PromptNER: Prompting for named entity recognition,'' in \emph{Proceedings of EMNLP}, 2023, pp. 2345--2356.
%
% \bibitem{chen2023contrastive}
% Y.~Chen et al., ``Contrastive in-context learning for few-shot NER,'' in \emph{Proceedings of NAACL}, 2023, pp. 3456--3467.
%
% \bibitem{tjong2003introduction}
% E.~F. Tjong Kim Sang and F.~De Meulder, ``Introduction to the CoNLL-2003 shared task: Language-independent named entity recognition,'' in \emph{Proceedings of CoNLL}, 2003, pp. 142--147.
%
% \bibitem{pradhan2013towards}
% S.~Pradhan et al., ``Towards robust linguistic analysis using OntoNotes,'' in \emph{Proceedings of CoNLL}, 2013, pp. 143--152.
%
% \bibitem{reimers2019sentence}
% N.~Reimers and I.~Gurevych, ``Sentence-BERT: Sentence embeddings using Siamese BERT-networks,'' in \emph{Proceedings of EMNLP}, 2019, pp. 3982--3992.
%
% \end{thebibliography}

% biography section
% 
% If you have an EPS/PDF photo (graphicx package needed) extra braces are
% needed around the contents of the optional argument to biography to prevent
% the LaTeX parser from getting confused when it sees the complicated
% \includegraphics command within an optional argument. (You could create
% your own custom macro containing the \includegraphics command to make things
% simpler here.)
%\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,clip,keepaspectratio]{mshell}}]{Michael Shell}
% or if you just want to reserve a space for a photo:

\begin{IEEEbiographynophoto}{Anonymous Author}
Anonymous Author is a graduate student in Computer Science at Anonymous University. Their research interests include natural language processing, in-context learning, and meta-learning applications in AI systems.
\end{IEEEbiographynophoto}

\begin{IEEEbiographynophoto}{Anonymous Advisor}
Anonymous Advisor is a Professor in the Department of Computer Science at Anonymous University. Their research focuses on machine learning, natural language processing, and artificial intelligence applications.
\end{IEEEbiographynophoto}

\begin{IEEEbiographynophoto}{Anonymous Collaborator}
Anonymous Collaborator is a Research Scientist with expertise in large language models and few-shot learning methodologies.
\end{IEEEbiographynophoto}

% You can push biographies down or up by placing
% a \vfill before or after them. The appropriate
% use of \vfill depends on what kind of text is
% on the last page and whether or not the columns
% are being equalized.

%\vfill

% Can be used to pull up biographies so that the bottom of the last one
% is flush with the other column.
%\enlargethispage{-5in}



% that's all folks
\end{document}
</file>

<file path="knowledge_base_config.json">
{
  "knowledge_base": {
    "main_file": "NER_Knowledge_Base.md",
    "index_file": "NER_Knowledge_Base_Index.md",
    "update_guide": "update_knowledge_base.md",
    "version": "1.0.0",
    "last_updated": "2024-12-28",
    "total_papers": 25
  },
  "sections": {
    "大语言模型与上下文学习": {
      "line_start": 13,
      "subsections": ["基础理论", "ICL机制研究"]
    },
    "少样本与零样本NER方法": {
      "line_start": 35,
      "subsections": ["基于LLM的NER框架", "零样本方法", "多智能体协作"]
    },
    "提示工程与示例选择": {
      "line_start": 89,
      "subsections": ["示例选择策略", "动态示例控制", "特殊提示技术", "对比学习方法"]
    },
    "元学习与多智能体方法": {
      "line_start": 149,
      "subsections": ["元学习框架"]
    },
    "检索增强与指令微调": {
      "line_start": 175,
      "subsections": ["指令微调方法", "检索增强生成", "嵌套NER专门方法"]
    },
    "综合性研究与工具": {
      "line_start": 227,
      "subsections": ["综述与调研", "实用工具", "统一框架"]
    },
    "技术挑战与解决方案": {
      "line_start": 258,
      "subsections": ["主要挑战", "应用扩展"]
    },
    "核心设计思想与选择策略": {
      "line_start": 353,
      "subsections": ["示例选择的哲学演进", "正负样本利用的思想转变", "任务建模的范式选择", "知识注入的策略选择", "上下文优化的理念", "标注指导的策略演进", "任务分解的设计理念", "质量控制的思想", "模型能力利用的理念"]
    },
    "关键技术趋势": {
      "line_start": 509,
      "subsections": ["范式转换", "多阶段精细化处理", "元学习与知识蒸馏", "智能示例选择策略演进", "多智能体协作架构", "检索增强与上下文优化", "领域适应与泛化能力", "理论与实践结合"]
    }
  },
  "key_methods": {
    "GPT-NER": {
      "authors": "Wang et al.",
      "year": 2023,
      "section": "少样本与零样本NER方法",
      "key_innovation": "原文改写格式，@@##标记",
      "bibkey": "wang2023gptner"
    },
    "PromptNER": {
      "authors": "Ashok & Lipton",
      "year": 2023,
      "section": "少样本与零样本NER方法",
      "key_innovation": "明确实体类型定义，Seq2Seq范式",
      "bibkey": "ashok2023promptner"
    },
    "C-ICL": {
      "authors": "Mo et al.",
      "year": 2024,
      "section": "提示工程与示例选择",
      "key_innovation": "对比学习，正负样本结合",
      "bibkey": "mo2024cicl"
    },
    "GuideNER": {
      "authors": "Huang et al.",
      "year": 2024,
      "section": "提示工程与示例选择",
      "key_innovation": "标注指南优于具体示例",
      "bibkey": "huang2024guidener"
    },
    "P-ICL": {
      "authors": "Jiang et al.",
      "year": 2024,
      "section": "提示工程与示例选择",
      "key_innovation": "点实体作为辅助信息",
      "bibkey": "jiang2024picl"
    },
    "VicunaNER": {
      "authors": "Ji",
      "year": 2023,
      "section": "少样本与零样本NER方法",
      "key_innovation": "两阶段框架：识别+再识别",
      "bibkey": "ji2023vicunaner"
    },
    "CMAS": {
      "authors": "Wang et al.",
      "year": 2025,
      "section": "少样本与零样本NER方法",
      "key_innovation": "四智能体协作框架",
      "bibkey": "wang2025cooperative"
    },
    "MetaIE": {
      "authors": "Peng et al.",
      "year": 2024,
      "section": "元学习与多智能体方法",
      "key_innovation": "符号蒸馏构建元模型",
      "bibkey": "peng2024metaie"
    },
    "2INER": {
      "authors": "Zhang et al.",
      "year": 2023,
      "section": "检索增强与指令微调",
      "key_innovation": "融合指令微调和上下文学习",
      "bibkey": "zhang2023iner"
    },
    "RA-IT": {
      "authors": "Xie et al.",
      "year": 2024,
      "section": "检索增强与指令微调",
      "key_innovation": "检索增强指令微调",
      "bibkey": "xie2024retrieval"
    },
    "EnDe Retriever": {
      "authors": "Zhang et al.",
      "year": 2024,
      "section": "检索增强与指令微调",
      "key_innovation": "三维相似性对比学习",
      "bibkey": "zhang2024incontext"
    },
    "IDS": {
      "authors": "Qin et al.",
      "year": 2024,
      "section": "提示工程与示例选择",
      "key_innovation": "迭代式演示选择",
      "bibkey": "qin2024incontext"
    },
    "D2Controller": {
      "authors": "Zhao et al.",
      "year": 2024,
      "section": "提示工程与示例选择",
      "key_innovation": "动态调整示例数量",
      "bibkey": "zhao2024dynamic"
    },
    "UIE": {
      "authors": "Lu et al.",
      "year": 2022,
      "section": "综合性研究与工具",
      "key_innovation": "统一文本到结构生成框架",
      "bibkey": "lu2022unified"
    }
  },
  "technical_concepts": {
    "上下文学习": ["ICL", "In-Context Learning"],
    "元函数": ["Meta Function", "λ(instruction, demonstrations, text)"],
    "原文改写": ["Text Rewriting", "@@## marking"],
    "硬负样本": ["Hard Negative Samples"],
    "点实体": ["Point Entities"],
    "类型相关特征": ["TRF", "Type-Related Features"],
    "符号蒸馏": ["Symbolic Distillation"],
    "三维相似性": ["Three-dimensional Similarity"],
    "迭代示例选择": ["Iterative Demonstration Selection"],
    "检索增强指令微调": ["RA-IT", "Retrieval Augmented Instruction Tuning"]
  },
  "update_tracking": {
    "next_bibkey_to_process": null,
    "pending_updates": [],
    "last_bib_file_hash": null,
    "auto_update_enabled": false
  },
  "search_patterns": {
    "method_patterns": [
      "**{authors} ({year})** - {title}",
      "- **核心创新**：",
      "- **技术方法**：",
      "- **实验结果**："
    ],
    "section_patterns": [
      "### {subsection_name}",
      "## {section_name}"
    ]
  }
}
</file>

<file path="NER_Knowledge_Base_Index.md">
# NER知识库索引系统

## 文献索引表

### A-C
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| adiga2024designing | Adiga et al. | 2024 | Designing Informative Metrics for Few-Shot Example Selection | 提示工程与示例选择 > 示例选择策略 | 复杂度匹配策略，ACL 2024接收 |
| ashok2023promptner | Ashok & Lipton | 2023 | PromptNER: Prompting For Named Entity Recognition | 少样本与零样本NER方法 > 基于LLM的NER框架 | 实体类型定义+链式思考，SOTA性能 |
| babych2003improving | Babych & Hartley | 2003 | Improving machine translation quality with automatic named entity recognition | 综合性研究与工具 > 应用扩展 | 机器翻译中的NER应用 |
| bai2025labelguided | Bai et al. | 2025 | Label-Guided In-Context Learning for Named Entity Recognition | 最新研究趋势与发现 > 标签引导的上下文学习 | DEER方法，标签引导示例选择 |
| brown2020language | Brown et al. | 2020 | Language Models are Few-Shot Learners | 大语言模型与上下文学习 > 基础理论 | ICL概念奠基 |
| chen2023learning | Chen et al. | 2023 | Learning In-context Learning for Named Entity Recognition | 元学习与多智能体方法 > 元学习框架 | 元函数预训练，ACL 2023接收 |
| chen2023selficl | Chen et al. | 2023 | Self-ICL: Zero-Shot In-Context Learning with Self-Generated Demonstrations | 少样本与零样本NER方法 > 零样本方法 | 自生成示例，EMNLP 2023接收 |

### D-H
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| huang2024guidener | Huang et al. | 2024 | GuideNER: Annotation Guidelines Are Better than Examples | 提示工程与示例选择 > 特殊提示技术 | 标注指南优于示例，AAAI 2025接收 |

### I-L
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| jiang2024picl | Jiang et al. | 2024 | P-ICL: Point In-Context Learning for Named Entity Recognition | 提示工程与示例选择 > 特殊提示技术 | 点实体辅助信息，K-Means选择策略 |
| ji2023vicunaner | Ji | 2023 | VicunaNER: Zero/Few-shot Named Entity Recognition using Vicuna | 少样本与零样本NER方法 > 零样本方法 | 开源模型两阶段框架，可复现方案 |
| keraghel2024recent | Keraghel et al. | 2024 | Recent Advances in Named Entity Recognition: A Comprehensive Survey | 综合性研究与工具 > 综述与调研 | 42页详尽分析，深度比较研究 |
| lee2022good | Lee et al. | 2022 | Good Examples Make A Faster Learner | 提示工程与示例选择 > 示例选择策略 | 系统研究示例策略三维度 |
| li2022rethinking | Li et al. | 2022 | Rethinking Negative Sampling for Handling Missing Entity Annotations | 技术挑战与解决方案 > 主要挑战 | 负采样处理漏标实体 |
| liu2021makesgood | Liu et al. | 2021 | What Makes Good In-Context Examples for GPT-3? | 大语言模型与上下文学习 > ICL机制研究 | 早期ICL示例选择研究 |
| lu2022unified | Lu et al. | 2022 | Unified Structure Generation for Universal Information Extraction | 综合性研究与工具 > 统一框架 | UIE统一文本到结构生成框架 |

### M-P
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| meng2019query | Meng et al. | 2019 | Query-Based Named Entity Recognition | 技术挑战与解决方案 > 主要挑战 | 查询式NER，机器阅读理解范式 |
| min2022rethinking | Min et al. | 2022 | Rethinking the Role of Demonstrations: What Makes In-Context Learning Work? | 大语言模型与上下文学习 > ICL机制研究 | 标签正确性影响有限的发现 |
| mo2024cicl | Mo et al. | 2024 | C-ICL: Contrastive In-context Learning for Information Extraction | 提示工程与示例选择 > 对比学习方法 | 对比学习框架，EMNLP 2024接收 |
| molla2006named | Molla et al. | 2006 | Named entity recognition in question answering of speech data | 综合性研究与工具 > 应用扩展 | 问答系统中的NER组件 |
| peng2024metaie | Peng et al. | 2024 | MetaIE: Distilling a Meta Model from LLM | 元学习与多智能体方法 > 元学习框架 | 符号蒸馏构建元模型 |

### Q-T
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| qin2024incontext | Qin et al. | 2024 | In-Context Learning with Iterative Demonstration Selection | 提示工程与示例选择 > 动态示例控制 | 迭代式演示选择(IDS) |
| rodrigues2024meta | Rodrigues & Branco | 2024 | Meta-prompting Optimized Retrieval-augmented Generation | 检索增强与指令微调 > 检索增强生成 | 元提示优化RAG，性能提升30%+ |
| guu2020realm | Guu et al. | 2020 | REALM: Retrieval-Augmented Language Model Pre-Training | 检索增强与指令微调 > 检索增强生成 | 首次提出RAG预训练方法 |

### U-Z
| BibTeX Key | 作者 | 年份 | 标题 | 知识库位置 | 核心贡献 |
|------------|------|------|------|------------|----------|
| villena2024llmner | Villena et al. | 2024 | llmNER: (Zero\|Few)-Shot Named Entity Recognition | 综合性研究与工具 > 实用工具 | Python库，简化提示工程和解析 |
| wang2023gptner | Wang et al. | 2023 | GPT-NER: Named Entity Recognition via Large Language Models | 少样本与零样本NER方法 > 基于LLM的NER框架 | 原文改写格式，实体级kNN检索 |
| wang2023sprompts | Wang et al. | 2023 | S-Prompts Learning with Pre-trained Transformers | 技术挑战与解决方案 > 主要挑战 | 独立跨域提示学习 |
| wang2025cooperative | Wang et al. | 2025 | A Cooperative Multi-Agent Framework for Zero-Shot Named Entity Recognition | 少样本与零样本NER方法 > 多智能体协作 | CMAS四智能体，WWW 2025接收 |
| xie2023empirical | Xie et al. | 2023 | Empirical Study of Zero-Shot NER with ChatGPT | 少样本与零样本NER方法 > 零样本方法 | 分解式问答，句法增强 |
| xie2024retrieval | Xie et al. | 2024 | Retrieval Augmented Instruction Tuning for Open NER | 检索增强与指令微调 > 检索增强生成 | RA-IT框架：检索增强指令微调 |
| zhang2023iner | Zhang et al. | 2023 | 2INER: Instructive and In-Context Learning | 检索增强与指令微调 > 指令微调方法 | 融合指令微调和上下文学习 |
| zhang2024incontext | Zhang et al. | 2024 | In-Context Learning for Few-Shot Nested Named Entity Recognition | 检索增强与指令微调 > 嵌套NER专门方法 | EnDe Retriever创新检索器 |
| zhao2024dynamic | Zhao et al. | 2024 | Dynamic Demonstrations Controller for In-Context Learning | 提示工程与示例选择 > 动态示例控制 | 动态调整示例数量 |

## 方法索引表

### 核心方法分类
| 方法名称 | 类型 | 主要特点 | 适用场景 | 性能特点 |
|----------|------|----------|----------|----------|
| GPT-NER | 生成范式 | 原文改写格式，@@##标记 | 少样本NER | 首次达到全监督性能 |
| PromptNER | 提示工程 | 明确类型定义，链式思考 | 需要详细指导的场景 | 4-9%绝对提升 |
| VicunaNER | 零样本 | 两阶段识别+再识别 | 开源模型零样本 | 解决数据泄露问题 |
| C-ICL | 对比学习 | 正负样本结合 | 需要边界学习的任务 | 显著性能改进 |
| GuideNER | 指南生成 | 标注指南优于示例 | 规则明确的任务 | 12.63个F1点提升 |
| P-ICL | 点实体 | K-Means聚类选择代表性实体词 | 信息密度要求高的场景 | 更高效的示例利用 |
| IDS | 迭代选择 | Zero-shot-CoT引导的动态选择 | 复杂推理任务 | 结合多样性和相似性 |
| D2Controller | 数量控制 | 动态调整示例数量 | 资源受限场景 | 平均4.6%相对提升 |
| CMAS | 多智能体 | 四智能体分工协作 | 复杂零样本任务 | 自反思质量控制 |
| MetaIE | 元学习 | 符号蒸馏小型元模型 | 需要轻量化部署 | 13个数据集SOTA |
| RA-IT | 检索增强 | 检索增强指令微调 | 开放域NER | 中英文双语验证 |
| EnDe Retriever | 嵌套NER | 三维相似性对比学习 | 嵌套实体场景 | 专门优化边界识别 |
| Self-ICL | 零样本 | 自生成示例 | 无标注数据场景 | 解决示例缺乏问题 |
| REALM | 检索增强 | 预训练阶段整合检索 | 知识密集型任务 | RAG方法基础 |

## 技术概念索引

### 核心技术概念
| 概念 | 定义 | 相关方法 | 应用价值 |
|------|------|----------|----------|
| 上下文学习(ICL) | 通过示例在上下文中学习任务 | GPT-NER, PromptNER等 | 免微调快速适应 |
| 元函数 | λ(instruction, demonstrations, text) → M1 | Chen et al. | 显式ICL能力建模 |
| 原文改写 | 用@@##标记的生成格式 | GPT-NER | 降低生成难度 |
| 硬负样本 | 看起来正确但实际错误的样本 | C-ICL | 增强边界学习 |
| 点实体 | 代表性实体词而非完整句子 | P-ICL | 提高信息密度 |
| 类型相关特征(TRF) | 与实体类型强相关的上下文特征 | CMAS | 任务分解优化 |
| 符号蒸馏 | 从LLM蒸馏元理解能力 | MetaIE | 轻量化部署 |
| 三维相似性 | 语义+边界+标签的综合相似性 | EnDe Retriever | 嵌套NER优化 |
| 迭代示例选择 | 基于推理路径的动态选择 | IDS | 自适应优化 |
| 检索增强指令微调 | 为训练样本检索相似上下文 | RA-IT | 开放域适应 |
| 自生成示例 | 模型自动生成演示样本 | Self-ICL | 零样本学习 |
| 检索增强预训练 | 预训练阶段整合检索机制 | REALM | 知识密集型任务 |

## 最新研究方法索引 (2024-2025)

### 新兴方法
| 方法名称 | 类型 | 主要特点 | 核心创新 | 应用场景 |
|----------|------|----------|----------|----------|
| GistScore | 表示学习 | 任务特定的示例选择表示 | 学习ICL专用嵌入空间 | 通用ICL任务 |
| HiTEC-ICL | 工具学习 | 分层工具选择与ICL结合 | 多工具场景的示例选择 | 复杂工具学习任务 |
| TABGEN-ICL | 结构化生成 | 残差感知示例选择 | 表格数据生成优化 | 结构化数据生成 |
| HiBench | 评估框架 | 多维度分层推理评估 | 层次结构理解评估 | 分层推理任务 |
| DEER | 标签引导 | 利用标签信息指导示例选择 | 标签分布匹配优化 | NER任务专门优化 |

### 理论进展
| 理论发现 | 来源 | 核心观点 | 实践意义 |
|----------|------|----------|----------|
| CoT有效性边界 | ICML 2024 Workshop | CoT在高质量示例下边际贡献有限 | 优先投资示例质量而非复杂推理 |
| 任务特定表示 | GistScore研究 | 通用嵌入不如任务特定表示 | 为不同任务学习专门的选择器 |
| 多维度评估重要性 | HiBench等 | 需要更全面的评估体系 | 建立任务特定的评估基准 |

## 更新日志

### 2025-01-22 重大更新
- 新增2024-2025年最新研究趋势分析
- 添加思维链有效性边界的理论发现
- 更新多维度示例选择的最新进展
- 新增跨领域应用扩展内容
- 建立新兴方法索引表

### 2024-12-28 初始版本
- 基于references.bib创建完整知识库
- 包含所有主要方法的详细分析
- 建立核心设计思想体系
- 创建完整索引系统

### 待更新项目
- [ ] 新增文献的自动索引更新
- [ ] 方法间关系图谱
- [ ] 性能对比表格
- [ ] 代码实现链接
- [ ] 数据集适用性分析
- [x] 2024-2025年最新研究趋势
- [x] 思维链有效性理论分析
- [x] 多维度选择方法进展

## 使用说明

1. **查找特定方法**：使用文献索引表按作者或年份查找
2. **了解技术类型**：使用方法索引表按类型筛选
3. **学习核心概念**：使用技术概念索引理解关键术语
4. **跟踪更新**：查看更新日志了解最新变化

## 维护指南

### 添加新文献时：
1. 在文献索引表中添加新条目
2. 在方法索引表中添加新方法（如适用）
3. 在技术概念索引中添加新概念（如适用）
4. 更新主知识库文件
5. 记录更新日志
</file>

<file path="NER_Knowledge_Base.md">
# 命名实体识别(NER)研究知识库

## 目录
1. [大语言模型与上下文学习](#大语言模型与上下文学习)
2. [少样本与零样本NER方法](#少样本与零样本ner方法)
3. [提示工程与示例选择](#提示工程与示例选择)
4. [元学习与多智能体方法](#元学习与多智能体方法)
5. [检索增强与指令微调](#检索增强与指令微调)
6. [综合性研究与工具](#综合性研究与工具)

---

## 大语言模型与上下文学习

### 基础理论
- **Brown et al. (2020)** - Language Models are Few-Shot Learners
  - 奠定了大语言模型少样本学习的理论基础
  - 提出了上下文学习(ICL)的概念

### ICL机制研究
- **Liu et al. (2021)** - What Makes Good In-Context Examples for GPT-3?
  - **发表信息**：arXiv:2101.06804，2021年1月提交
  - **核心问题**：系统研究什么样的示例对GPT-3的上下文学习最有效
  - **主要发现**：
    - **相似性重要性**：与测试样本相似的示例效果更好
    - **多样性平衡**：需要在相似性和多样性之间找到平衡
    - **示例质量**：高质量示例比数量更重要
    - **任务相关性**：不同任务对示例选择策略的需求不同
  - **方法论贡献**：
    - 提出了系统性的示例选择评估框架
    - 建立了示例质量的量化指标
    - 为后续ICL研究奠定了基础
  - **影响意义**：这是最早系统研究ICL示例选择的重要工作之一

- **Min et al. (2022)** - Rethinking the Role of Demonstrations: What Makes In-Context Learning Work?
  - **颠覆性发现**：随机替换示例中的标签对性能影响很小，挑战了传统认知
  - **系统性实验**：在12个不同模型（包括GPT-3）上验证，涵盖分类和多选任务
  - **真正重要的因素**：
    - **标签空间（Label Space）**：让模型了解可能的输出类别
    - **输入分布（Input Distribution）**：提供输入文本的分布信息
    - **序列格式（Overall Format）**：整体的输入-输出格式规范
  - **标签正确性影响有限**：即使使用完全随机的标签，性能下降也很小
  - **理论意义**：重新定义了对ICL工作机制的理解
  - **实践指导**：为ICL的设计和优化提供了新的方向
  - **后续影响**：启发了大量关于ICL机制的深入研究

---

## 少样本与零样本NER方法

### 基于LLM的NER框架
- **Wang et al. (2023)** - GPT-NER: Named Entity Recognition via Large Language Models
  - **核心创新**：将NER重构为文本生成任务，避免传统序列标注的复杂性
  - **原文改写格式**：要求LLM生成与输入几乎相同的文本，但用@@和##标记实体
    - 例：从"Columbus is a city"生成"@@Columbus## is a city"
    - 大幅降低生成难度，模型只需"复制粘贴+添加标记"
  - **三种检索策略对比**：
    - 随机检索：效果最差
    - 句子级kNN：检索语义相似句子
    - 实体级kNN：用预训练NER模型识别潜在实体，检索包含相似实体的句子（推荐）
  - **自我验证机制**：两阶段流程解决LLM幻觉问题
    - 第一阶段：实体抽取
    - 第二阶段：验证性提问，如"'Hendrix'是地点实体吗？"
  - **性能突破**：首次在ICL方法上达到与全监督模型相当的性能

- **Ashok & Lipton (2023)** - PromptNER: Prompting For Named Entity Recognition
  - **发表信息**：arXiv:2305.15444，2023年5月24日首次提交，6月20日最终版本
  - **核心问题**：传统ICL在NER上表现不佳，因为仅提供示例不足以让LLM理解特定领域中"什么才算是一个实体"
  - **解决方案**：除了提供少量示例外，还必须提供明确的、模块化的实体类型定义（entity definitions）
  - **主要方法**：
    - **模块化定义**：为每个NER任务创建自然语言的定义文档，详细描述每个实体类型应该包含什么、不应该包含什么
    - **链式思考式输出**：要求LLM不仅识别实体，还要对每个候选实体做出判断并提供解释
    - **Seq2Seq范式**：将NER任务建模为序列到序列的生成任务，更好地处理多词实体
  - **性能表现**：
    - CoNLL数据集上F1分数绝对提升4%
    - GENIA数据集上绝对提升9%
    - FewNERD数据集上绝对提升4%
    - 在跨域NER上设置新的SOTA，平均F1提升3%
  - **低资源优势**：8个训练样本时F1达到60，监督模型几乎为0

- **Ashok & Lipton (2023)** - PromptNER: Prompting For Named Entity Recognition
  - **核心观点**：传统ICL在NER上表现不佳，因为仅提供示例不足以让LLM理解"什么是实体"
  - **模块化定义**：为每个NER任务创建自然语言定义文档，详述每个实体类型的包含/排除标准
  - **链式思考输出**：要求LLM不仅识别实体，还要对每个候选实体提供判断解释
  - **Seq2Seq范式**：将NER建模为序列到序列生成，更好处理多词实体
  - **性能提升**：ConLL数据集4%绝对提升，GENIA数据集9%绝对提升，FewNERD数据集4%绝对提升

### 零样本方法
- **Chen et al. (2023)** - Self-ICL: Zero-Shot In-Context Learning with Self-Generated Demonstrations
  - **发表信息**：EMNLP 2023主会议接收
  - **核心创新**：提出自生成示例实现零样本上下文学习
  - **Self-ICL方法**：
    - 无需人工标注的示例，模型自己生成演示样本
    - 通过自我生成的示例进行上下文学习
    - 解决了零样本场景下缺乏高质量示例的问题
  - **技术特点**：
    - 自动生成与测试样本相关的演示
    - 迭代优化生成的示例质量
    - 在多个NLP任务上验证有效性
  - **应用价值**：为真正的零样本学习提供了新的技术路径

- **Ji (2023)** - VicunaNER: Zero/Few-shot Named Entity Recognition using Vicuna
  - **发表信息**：arXiv:2305.03253，2023年5月5日提交
  - **动机**：解决在线API模型（如ChatGPT）的数据泄露和不可复现问题，基于开源Vicuna模型
  - **两阶段框架**：
    - 阶段一（识别）：多轮对话进行初次实体识别，包含正确性检查环节过滤错误实体
    - 阶段二（再识别）：将第一阶段结果作为已知信息，专门识别遗漏的实体
  - **实体合并**：两阶段结果合并去重得到最终输出
  - **性能验证**：在10个跨5个领域的数据集上验证零样本能力，Few-NERD上验证少样本能力
  - **多角度分析**：从多个维度对Vicuna进行全面调研
  - **开源优势**：提供了基于开源模型的可复现NER解决方案，避免了商业API的限制

- **Xie et al. (2023)** - Empirical Study of Zero-Shot NER with ChatGPT
  - **核心理念**：激发ChatGPT的推理能力，而非简单直接输出
  - **分解式问答（Decomposed-QA）**：将复杂的多类型实体抽取分解为多轮单类型对话
    - 每轮只抽取一种实体类型，降低任务复杂度
    - 将前轮问答作为后续轮次的上下文
  - **句法增强（Syntactic Augmentation）**：
    - 句法提示：鼓励模型先分析句法结构再识别
    - 工具增强：使用外部解析工具（如Hanlp）提供词性标注、依存树等信息
  - **两阶段多数投票**：针对自洽性的优化策略
    - 第一阶段：对实体本身（mention）投票
    - 第二阶段：对实体类型（type）投票
  - **广泛验证**：在7个基准数据集（中英文、领域特定和通用领域）上验证有效性

### 多智能体协作
- **Wang et al. (2025)** - A Cooperative Multi-Agent Framework for Zero-Shot Named Entity Recognition
  - **发表信息**：arXiv:2502.18702，2025年2月25日提交，已被WWW 2025会议接收
  - **核心挑战**：
    - 实体周围上下文的相关性被忽略，导致错误的类型预测或实体遗漏
    - 基于浅层相似性策略检索的示例使用不当，严重误导LLM推理
  - **CMAS四智能体架构**：
    - **自标注器（Self-annotator）**：在无标签数据上进行初步实体标注，创建伪标签数据集
    - **类型相关特征提取器（TRF Extractor）**：重新定义NER任务，不仅识别实体还识别与实体类型强相关的上下文特征
    - **示例判别器（Demonstration Discriminator）**：引入自反思机制，评估检索示例对当前测试样本的有效性得分
    - **总预测器（Overall Predictor）**：综合TRF、示例有效性得分等信息做出最终NER预测
  - **任务重构**：将NER分解为两个子任务
    - 识别命名实体
    - 识别实体类型相关特征
  - **自反思机制**：自动评估示例帮助度，避免不相关示例误导
  - **广泛验证**：在6个基准数据集上验证，包括领域特定和通用领域场景
  - **扩展性**：在少样本设置和不同LLM骨干网络上都展现有效性
  - **集体智能**：通过多个智能体的协作解决单一LLM的局限性

---

## 提示工程与示例选择

### 示例选择策略
- **Adiga et al. (2024)** - Designing Informative Metrics for Few-Shot Example Selection
  - **发表信息**：ACL 2024 Findings接收，arXiv:2403.03861
  - **核心思想**：避免训练专门的示例选择模型，而是用复杂度指标对齐测试句子和示例的句法-语义复杂度
  - **复杂度匹配原理**：选择与测试句子复杂度相似的示例，而非简单的语义相似性
  - **双层次指标体系**：
    - **句子级指标**：整体句法结构复杂度、语义密度、句子长度等
    - **词级指标**：词汇复杂度、实体密度、句法角色、词性分布等
  - **避免模型训练**：不需要额外训练示例选择器，直接使用可计算的复杂度指标
  - **显著提升**：GPT-4在CoNLL2003上F1绝对提升5%，GPT-j-6B等小模型提升高达28.85点
  - **普适性**：在序列标注任务上展现出强大的泛化能力
  - **实用价值**：为少样本学习提供了简单有效的示例选择策略

- **Lee et al. (2022)** - Good Examples Make A Faster Learner
  - **系统性研究框架**：首次系统研究示例策略的三个核心维度
  - **内容维度（What to include）**：
    - 实体示例：仅包含实体词 vs 包含完整上下文
    - 上下文范围：局部上下文 vs 完整句子
    - 标注格式：不同的实体标记方式
  - **选择维度（How to select）**：
    - 随机选择 vs 基于相似性选择
    - 领域内选择 vs 跨领域选择
    - 平衡性考虑：不同实体类型的分布
  - **模板维度（What templates）**：
    - 指令模板的设计
    - 示例排列顺序的影响
    - 格式一致性的重要性
  - **关键发现**：
    - 一致性比多样性更重要
    - 好的示例可以节省大量标注样本
    - 示例质量对低资源设置影响更大
  - **实用指导**：为实际应用提供了具体的示例选择指南

### 动态示例控制
- **Qin et al. (2024)** - In-Context Learning with Iterative Demonstration Selection
  - **核心洞察**：最佳示例选择标准（多样性vs相似性）因任务而异
  - **IDS迭代框架**：
    - **步骤1**：对测试样本使用Zero-shot-CoT生成初步推理路径
    - **步骤2**：用推理路径作为Query检索语义最相似的k个训练样本作为示例
    - **步骤3**：用新示例和原样本再次提问LLM，生成新答案和推理路径
    - **步骤4**：用新推理路径重复步骤2，进行下一轮示例选择
    - **步骤5**：迭代数次后，对所有生成答案进行多数投票
  - **优势结合**：既保证示例多样性又确保与测试样本强相关
  - **广泛验证**：在推理、问答、主题分类等任务上持续优于现有ICL示例选择方法

- **Zhao et al. (2024)** - Dynamic Demonstrations Controller for In-Context Learning
  - **颠覆性发现**：示例数量与性能并非总是正相关，挑战"越多越好"传统观念
  - **D2Controller方法**：动态调整示例数量而非固定使用最大可能数量
  - **核心机制**：根据任务特性和输入长度限制智能确定最优示例数量
  - **性能提升**：在10种不同规模LLM和10个数据集上平均相对提升4.6%
  - **扩展性**：可与现有ICL模型结合，取得竞争性结果
  - **实用价值**：为ICL实践提供了新的优化维度

### 特殊提示技术
- **Huang et al. (2024)** - GuideNER: Annotation Guidelines Are Better than Examples for In-Context Named Entity Recognition
  - **发表信息**：已被AAAI 2025会议接收，2025年4月11日发表
  - **核心观点**：对于NER任务，明确的标注指南比具体示例更有效
  - **问题分析**：LLM难从有限具体例子中正确归纳抽象标注规则，且不能学习特定的输入-标签映射
  - **三步法生成指南**：
    - 总结模式：从训练集每个（输入-标签）对中总结通用规则
    - 自我验证：验证规则是否能准确预测原始标签，过滤有歧义规则
    - 聚合规则：统计验证通过规则的频率，选择Top-K条最通用规则
  - **显著提升**：GENIA数据集上提升12.63个F1点，且Prompt长度更短
  - **方法优势**：避免传统ICL中示例选择的复杂性，直接提供抽象指导
  - **一致性发现**：在广泛使用的NER数据集上都能持续显著优于SOTA方法

- **Jiang et al. (2024)** - P-ICL: Point In-Context Learning for Named Entity Recognition with Large Language Models
  - **发表信息**：arXiv:2405.04960，2024年5月8日提交，6月17日更新
  - **核心洞察**：标准ICL的完整句子示例信息冗余，忽略了NER任务特殊性
  - **Point Entities概念**：为每个实体类型提供代表性实体词作为示例
    - 例：LOC类型提供"London, China, California"
    - 比完整句子更直接、信息密度更高
  - **K-Means选择策略**：
    - 用BERT等模型将同类实体编码为向量
    - 进行K-Means聚类
    - 选择离各簇中心最近的实体词作为Point Entities
  - **框架设计**：在Prompt中结合任务指令、少量句子示例和Point Entities
  - **实验验证**：在多个代表性NER基准上验证P-ICL和点实体选择方法的有效性
  - **精确分类**：通过显著信息帮助LLM更精确地进行实体分类

### 对比学习方法
- **Mo et al. (2024)** - C-ICL: Contrastive In-context Learning for Information Extraction
  - **发表信息**：EMNLP 2024 Findings接收，arXiv:2402.11254
  - **核心洞察**：传统ICL只关注正确示例，忽略了错误示例的价值
  - **对比学习框架**：同时利用正确和错误样本构建示例，增强LLM的判别能力
  - **硬负样本选择策略**：
    - 使用大模型为标注数据生成标签
    - 识别那些"看起来正确但实际错误"的样本作为硬负样本
    - 这些样本能更好地帮助模型学习边界情况
  - **正样本选择策略**：
    - 选择与测试样本最相似的正确标注样本
    - 基于语义相似性的最近邻搜索
  - **示例构建方法**：
    - 将硬负样本和最近邻正样本组合构建演示
    - 明确标注哪些是正确示例，哪些是错误示例
    - 要求模型不仅识别实体，还要解释为什么某些候选是错误的
  - **推理增强**：结合推理过程，让模型理解正确和错误判断的原因
  - **广泛适用**：在NER和关系抽取等多个IE任务上验证有效性
  - **显著提升**：在各种数据集上相比传统ICL方法取得实质性性能改进
  - **多样性价值**：展示了负样本在ICL中的重要作用

---

## 元学习与多智能体方法

### 元学习框架
- **Chen et al. (2023)** - Learning In-context Learning for Named Entity Recognition
  - **发表信息**：ACL 2023主会议接收，arXiv:2305.11038
  - **核心理念**：显式地为PLM注入上下文学习能力，而非依赖隐式涌现
  - **元函数建模**：将PLM建模为元函数λ(instruction, demonstrations, text) → M1
  - **动态实体抽取器构建**：
    - 新抽取器 = (λ.M)(instruction, demonstrations) → F
    - F: text → entities（从文本到实体的映射）
  - **元函数预训练算法**：
    - 通过比较(instruction, demonstration)初始化的抽取器与代理黄金抽取器进行预训练
    - 使PLM学会如何根据指令和示例快速适应新的NER任务
  - **实验验证**：在4个少样本NER数据集上显著优于PLM+微调方法
  - **能力注入**：成功将上下文NER能力注入到PLM中
  - **理论贡献**：首次系统性地将ICL能力作为可学习的技能注入到预训练模型中

- **Peng et al. (2024)** - MetaIE: Distilling a Meta Model from LLM
  - **统一观察**：所有IE任务本质上都是"标签到跨度匹配"问题
  - **符号蒸馏方法**：
    - 从语言模型预训练数据集（如OpenWebText）采样句子
    - 用LLM识别"重要信息"的类型化跨度
    - 构建蒸馏数据集训练小型元模型
  - **元理解概念**：让小模型学习提取"重要信息"的元理解能力
  - **少样本适应**：元模型可高效适应各种IE任务
  - **全面评估**：在13个数据集、6个IE任务上验证，包括监督、低资源、少样本设置
  - **优势对比**：超越香草语言模型预训练、多IE任务人工标注预训练、单IE任务符号蒸馏等方法

### 指令微调方法
- **Zhang et al. (2023)** - 2INER: Instructive and In-Context Learning
  - **问题识别**：现有方法对实体类型在句子全局语义下的理解不够深入，未充分利用ICL优势
  - **融合策略**：将指令微调和上下文学习两种技术有机结合
  - **新辅助任务：类型抽取（Type Extraction）**：
    - 动机：补强对标签与句子语义关系的理解
    - 任务定义：不关注具体实体边界，直接判断句子中包含哪些实体类型
    - 例：对"are there any cheap 4 star restaurants near here"输出"rating type exists"、"location type exists"
    - 效果：迫使模型从整体语义层面理解实体类型含义
  - **ICL集成**：
    - 在主NER任务输入中添加"示例（Examples）"字段
    - 以自然语言形式呈现，如"'modills' is a restaurant name"
    - 训练时为每种实体类型提供至少一个示例，推理时从训练支持集采样
  - **三步NER分解**：理解标签语义关系 → 识别实体边界 → 实体类型标注
  - **性能验证**：在四个数据集上超越现有少样本NER方法，与SOTA标准NER算法竞争

---

## 检索增强与指令微调

### 检索增强生成
- **Guu et al. (2020)** - REALM: Retrieval-Augmented Language Model Pre-Training
  - **发表信息**：arXiv:2002.08909，2020年2月提交
  - **核心创新**：首次提出检索增强的语言模型预训练方法
  - **REALM架构**：
    - 结合神经检索器和语言模型
    - 在预训练阶段就整合检索机制
    - 端到端训练检索和生成组件
  - **技术特点**：
    - 动态检索相关文档作为上下文
    - 可解释的知识获取过程
    - 在开放域问答任务上显著提升性能
  - **影响意义**：为后续RAG方法奠定了理论基础

- **Rodrigues & Branco (2024)** - Meta-prompting Optimized Retrieval-augmented Generation
  - **发表信息**：arXiv:2407.03955，2024年7月4日提交
  - **问题分析**：检索到的内容可能冗长、杂乱，直接拼接到Prompt中效果不佳
  - **三LLM架构**：
    - **生成LLM**：执行最终的问答任务
    - **转换LLM**：接收检索文档和精炼指令，输出精炼后内容（摘要、核心观点等）
    - **优化器LLM**：生成并优化给转换LLM的精炼指令
  - **元提示机制**：
    - 优化器LLM通过元提示工作，包含"生成更好精炼指令"的元指令
    - 参考当前最优精炼指令列表及其历史表现分数
    - 迭代生成新的、可能更好的精炼指令
  - **性能提升**：在StrategyQA多跳问答任务上比无此方法的RAG系统提升超30%
  - **创新意义**：为RAG系统的内容精炼提供了自动化优化方案

- **Xie et al. (2024)** - Retrieval Augmented Instruction Tuning for Open NER
  - **核心观点**：仅使用原始指令进行微调不够，需要检索增强
  - **RA-IT框架**：
    - 对每个训练样本，用基于句子嵌入的检索器（如GTE-large）找到k个语义最相似样本
    - 将检索样本作为上下文拼接到原始指令微调模板之前
    - 用这种"增强后"数据对LLM（LLaMA-3, Qwen-1.5）进行微调
  - **双语验证**：构建中文IT数据集，在中英文场景下验证RA-IT有效性
  - **检索策略研究**：深入探索各种检索策略在RA-IT框架中的影响
  - **数据规模分析**：验证RA-IT在不同数据规模下的表现

### 嵌套NER专门方法
- **Zhang et al. (2024)** - In-Context Learning for Few-Shot Nested Named Entity Recognition
  - **专门挑战**：嵌套NER中实体可能重叠或包含，传统ICL示例选择策略不适用
  - **EnDe Retriever创新**：专为少样本嵌套NER设计的先进检索框架
  - **三维相似性对比学习**：
    - **语义相似性**：确保检索示例在句子语义上与测试样本相近
    - **边界相似性**：关键创新，通过词性标注(POS tags)和句法成分树(constituency trees)等特征，让边界结构相似的句子在表示空间中更接近
    - **标签相似性**：通过对比学习拉近相同实体标签的示例，推开文本重叠但标签不同的实体示例
  - **对比学习机制**：综合三个维度优化检索过程，减少歧义
  - **精细化策略**：为模型提供更好的实体边界线索，特别适合嵌套场景
  - **兼容性验证**：方法进步可兼容一般少样本NER设置
  - **显著提升**：在极少量标注数据情况下实现显著性能提升

---

## 综合性研究与工具

### 综述与调研
- **Keraghel et al. (2024)** - Recent Advances in Named Entity Recognition: A Comprehensive Survey and Comparative Study
  - **发表信息**：arXiv:2401.10825，2024年1月19日首次提交，12月20日最终版本
  - **全面覆盖**：首次系统性涵盖Transformer和LLM在NER中的最新进展
  - **方法分类**：
    - Transformer-based方法的进展
    - 大语言模型(LLMs)在NER中的应用（此前综述覆盖不足）
    - 强化学习方法在NER性能提升中的作用
    - 图方法(Graph-based approaches)的创新应用
  - **稀缺标注重点**：专门关注为稀缺标注数据集设计的方法
  - **深度比较**：对从未一起考虑过的算法进行深度性能比较
  - **数据集特征分析**：在不同特征数据集上评估性能
    - 领域差异（domain）
    - 数据规模（size）
    - 类别数量（number of classes）
  - **实证洞察**：揭示数据集特征如何影响不同方法的行为表现
  - **42页详尽分析**：提供NER领域最全面的现代技术综述
  - **比较研究价值**：为研究者选择合适方法提供了重要参考

### 实用工具
- **Villena et al. (2024)** - llmNER: (Zero|Few)-Shot Named Entity Recognition, Exploiting the Power of Large Language Models
  - **发表信息**：arXiv:2406.04528，2024年6月6日提交
  - **工具定位**：Python库实现零样本和少样本NER，利用大语言模型的强大能力
  - **核心功能**：
    - 提供易用接口组合提示、查询模型、解析LLM返回的完成结果
    - 支持高效的提示工程，提供简单接口测试多个变量
    - 移除提示和解析步骤的障碍，推动上下文学习研究边界
  - **验证范围**：在两个NER任务上验证软件的灵活性
  - **研究价值**：为NER的上下文学习研究提供了实用的工具支持

### 统一框架
- **Lu et al. (2022)** - Unified Structure Generation for Universal Information Extraction
  - **核心问题**：IE任务目标多样、结构异构、模式特定，难以统一处理
  - **UIE框架**：统一文本到结构生成框架，可普遍建模不同IE任务
  - **三大创新**：
    - **结构化抽取语言**：统一编码不同抽取结构的表示方法
    - **模式化提示机制（Structural Schema Instructor）**：自适应生成目标抽取结构
    - **大规模预训练**：通过大规模预训练文本到结构模型捕获通用IE能力
  - **任务统一**：将NER、关系抽取、事件抽取、情感抽取等统一为结构生成任务
  - **全面验证**：在4个IE任务、13个数据集上达到SOTA性能
  - **设置覆盖**：在监督、低资源、少样本等所有设置下都验证了有效性
  - **能力验证**：证明了UIE的有效性、普遍性和可迁移性

---

## 技术挑战与解决方案

### 主要挑战
1. **标注不完整问题**
   - **Li et al. (2022)**: Rethinking Negative Sampling for Handling Missing Entity Annotations
     - **问题分析**：训练数据中存在大量"漏标"实体（本应标注但未标注）
     - **负采样策略**：不把所有未标注片段当负样本，而是随机采样一小部分
     - **关键概念**：
       - 错采样率(Missampling Rate)：采样负样本中包含漏标实体的比例
       - 不确定性(Uncertainty)：采样模型最不确定的负样本效果更好
     - **加权采样**：基于不确定性高且错采样率低的原则设计自适应采样方法
     - **理论贡献**：基于实体稀疏性推导零错采样率的概率下界

2. **嵌套实体处理**
   - **Meng et al. (2019)**: Query-Based Named Entity Recognition
     - **问题解决**：传统序列标注无法处理重叠/嵌套实体
     - **查询范式**：将NER重构为机器阅读理解任务
     - **具体方法**：如抽取PER实体转化为回答"文中提到了哪个人？"
     - **优势**：自然处理同一token参与多个实体类别的情况
     - **SOTA成果**：在5个中英文数据集上创造新的最佳结果

3. **领域适应性**
   - **Wang et al. (2023)**: S-Prompts Learning with Pre-trained Transformers
     - **核心理念**：独立学习跨域提示，避免传统方法中的样本存储
     - **Occam剃刀原则**：简单有效的域增量学习范式
     - **独立提示**：每个域独立学习提示，实现最优性能
     - **极低参数增长**：每个域仅增加0.03%参数
     - **显著提升**：比最佳无样本方法平均提升30%，比有样本方法提升6%

### 应用扩展
- **Babych & Hartley (2003)**: 机器翻译中的NER应用
- **Molla et al. (2006)**: 问答系统中的NER组件

---

## 核心设计思想与选择策略

### 1. 示例选择的哲学演进

#### 传统随机选择 → 语义相似性
- **问题**：随机选择无法保证示例与测试样本的相关性
- **解决**：基于语义相似性（如句子级kNN）选择最相关示例
- **局限**：语义相似不等于任务相关，可能选到意思相近但实体类型不同的句子

#### 语义相似性 → 任务特定相似性
- **GPT-NER的实体级kNN思想**：
  - 不看整句相似性，而看实体相似性
  - 用预训练NER模型识别潜在实体，基于实体表示检索
  - 确保检索到的示例包含相似的实体类型和模式
- **P-ICL的Point Entities思想**：
  - 完整句子信息冗余，直接提供实体词更高效
  - 为每个类型提供代表性实体词，信息密度更高
  - K-Means聚类确保代表性，避免偏向高频实体

#### 单维相似性 → 多维综合相似性
- **EnDe Retriever的三维思想**：
  - 语义维度：确保上下文相关性
  - 边界维度：通过句法特征确保结构相似性（专为嵌套NER设计）
  - 标签维度：通过对比学习拉近同类、推开异类
- **复杂度匹配思想**：
  - 不追求语义相似，而追求复杂度匹配
  - 句子级和词级双层次复杂度指标
  - 避免简单句子配复杂示例或反之的不匹配问题

### 2. 正负样本利用的思想转变

#### 仅用正样本 → 正负样本结合
- **传统ICL局限**：只展示正确示例，模型难以学习边界
- **C-ICL的对比思想**：
  - 硬负样本：选择"看起来对但实际错"的样本
  - 让模型同时看到正确和错误的判断过程
  - 增强模型的判别能力和边界感知

#### 静态示例 → 动态自适应
- **IDS的迭代思想**：
  - 不预先固定示例，而是根据模型推理过程动态调整
  - 用Zero-shot-CoT生成的推理路径指导示例检索
  - 多轮迭代优化，结合多样性和相似性优势
- **D2Controller的数量控制思想**：
  - 挑战"示例越多越好"的传统观念
  - 根据任务特性和输入长度限制智能确定最优数量
  - 避免信息过载和注意力分散

### 3. 任务建模的范式选择

#### 序列标注 → 生成范式
- **GPT-NER的生成思想**：
  - 序列标注对LLM来说不自然，生成任务更符合预训练目标
  - "原文改写"降低生成难度，保持输入输出的对应关系
  - 自然处理多词实体和嵌套实体问题

#### 直接输出 → 多阶段验证
- **两阶段验证思想**：
  - GPT-NER：识别 + 验证，减少幻觉
  - VicunaNER：识别 + 再识别，提升召回率
- **三阶段指南生成思想**（GuideNER）：
  - 总结模式 → 自我验证 → 聚合规则
  - 从具体示例抽象出通用指南，避免示例选择复杂性

### 4. 知识注入的策略选择

#### 隐式学习 → 显式注入
- **传统ICL问题**：依赖模型隐式学习，能力不稳定
- **元学习思想**（Chen et al.）：
  - 显式训练模型的ICL能力
  - 将PLM建模为元函数，学习如何根据指令和示例快速适应
- **符号蒸馏思想**（MetaIE）：
  - 从大模型蒸馏"重要信息提取"的元理解
  - 小模型学习通用IE能力，再适应具体任务

#### 单一指导 → 多智能体协作
- **CMAS的分工思想**：
  - 复杂任务分解为多个子任务
  - 每个智能体专门处理一个方面
  - 自反思机制确保质量控制

### 5. 上下文优化的理念

#### 简单拼接 → 智能精炼
- **元提示优化思想**（Rodrigues & Branco）：
  - 检索内容需要精炼，不是越多越好
  - 三LLM架构：生成、转换、优化器
  - 迭代优化精炼指令，实现内容质量的持续改进

#### 原始指令 → 检索增强
- **RA-IT思想**：
  - 指令微调时不仅用原始样本，还要检索相似样本作为上下文
  - 为每个训练样本提供丰富的上下文信息
  - 增强模型对任务模式的理解

### 6. 标注指导的策略演进

#### 具体示例 → 抽象指南
- **GuideNER的指南思想**：
  - 问题：LLM难从有限具体例子中正确归纳抽象规则
  - 解决：直接提供明确的标注指南而非具体示例
  - 优势：避免示例选择复杂性，提供更直接的指导

#### 简单定义 → 详细说明
- **PromptNER的定义思想**：
  - 传统ICL仅提供示例，不足以让LLM理解"什么是实体"
  - 为每个实体类型创建详细的自然语言定义文档
  - 包含包含/排除标准，消除歧义

### 7. 任务分解的设计理念

#### 端到端 → 分步处理
- **分解式问答思想**（Xie et al.）：
  - 复杂的多类型实体抽取分解为多轮单类型对话
  - 每轮只抽取一种实体类型，降低任务复杂度
  - 前轮结果作为后续轮次的上下文

#### 单任务 → 辅助任务
- **2INER的类型抽取思想**：
  - 主任务：实体识别和边界确定
  - 辅助任务：类型抽取（判断句子中包含哪些实体类型）
  - 辅助任务增强对实体类型语义的理解

### 8. 质量控制的思想

#### 盲目信任 → 自我验证
- **自反思机制**：
  - GPT-NER：验证性提问确认实体类型
  - CMAS：示例判别器评估示例有效性
  - 多轮对话中的正确性检查

#### 单次输出 → 多数投票
- **集成思想**：
  - 两阶段多数投票：先对实体本身投票，再对类型投票
  - 多轮迭代后的结果聚合
  - 提升结果的稳定性和可靠性

### 9. 模型能力利用的理念

#### 被动适应 → 主动引导
- **Chain-of-Thought思想**：
  - 不仅要求输出结果，还要求解释推理过程
  - 激发模型的推理能力而非简单模式匹配
  - 提供可解释的决策过程

#### 单模型 → 多模型协作
- **三LLM架构思想**：
  - 生成LLM：执行最终任务
  - 转换LLM：精炼检索内容
  - 优化器LLM：优化精炼指令
  - 各司其职，发挥各自优势

---

## 关键技术趋势

### 1. 范式转换：从序列标注到生成范式
- **GPT-NER突破**：用@@和##标记的"原文改写"格式，将复杂序列标注简化为"复制+标记"
- **PromptNER创新**：Seq2Seq范式更好处理多词实体，结合链式思考提供判断解释
- **UIE统一**：将所有IE任务统一为文本到结构生成，实现跨任务知识共享

### 2. 多阶段精细化处理
- **两阶段验证**：GPT-NER的识别-验证，VicunaNER的识别-再识别
- **三阶段指南生成**：GuideNER的总结-验证-聚合流程
- **迭代优化**：IDS的多轮示例选择，元提示的指令迭代优化

### 3. 元学习与知识蒸馏
- **显式能力注入**：Chen et al.通过元函数预训练显式注入ICL能力
- **符号蒸馏**：MetaIE从LLM蒸馏小型元模型，学习"重要信息"提取的元理解
- **混合方法**：2INER融合指令微调和上下文学习，发挥两者优势

### 4. 智能示例选择策略演进
- **第一代**：随机选择（效果最差）
- **第二代**：语义相似性（句子级kNN）
- **第三代**：任务特定相似性（实体级kNN、复杂度匹配）
- **第四代**：多维度综合（EnDe的语义-边界-标签三维相似性）
- **第五代**：动态自适应（D2Controller的数量控制、IDS的迭代选择）

### 5. 多智能体协作架构
- **CMAS四智能体**：自标注器、TRF提取器、示例判别器、总预测器的分工协作
- **自反思机制**：智能体间的相互验证和质量评估
- **任务分解**：将复杂NER分解为多个子任务，各智能体专门处理

### 6. 检索增强与上下文优化
- **RA-IT框架**：检索增强指令微调，为每个训练样本提供相似样本上下文
- **元提示优化**：三LLM架构实现检索内容的智能精炼
- **上下文质量控制**：从简单拼接到智能筛选和动态调整

### 7. 领域适应与泛化能力
- **独立域学习**：S-Prompts的跨域独立提示学习
- **开放域NER**：RA-IT等方法专门针对开放域场景设计
- **嵌套实体处理**：EnDe Retriever等专门解决复杂嵌套场景

### 8. 理论与实践结合
- **理论分析**：负采样的错采样率理论、ICL机制的深入分析
- **实用工具**：llmNER等Python库降低应用门槛
- **全面评估**：跨语言、跨领域、多设置的系统性验证

## 最新研究趋势与发现 (2024-2025)

### 思维链与ICL有效性的理论分析
- **理论突破**：2024年ICML Workshop on In-Context Learning提出了思维链(CoT)对ICL影响的理论分析
  - **核心发现**：CoT能够使transformer模型学习到原本无法学习的概念
  - **机制解释**：CoT通过提供中间推理步骤，帮助模型建立输入-输出之间的复杂映射关系
  - **局限性分析**：当示例质量足够高时，CoT的边际贡献可能有限

### 多维度示例选择的新进展
- **GistScore方法** (ICML 2024)：
  - **核心理念**：学习更好的表示用于上下文示例选择
  - **技术创新**：不依赖简单的语义相似度，而是学习任务特定的表示
  - **性能提升**：在多个ICL任务上显著优于传统k-NN方法

### 分层结构推理能力评估
- **HiBench基准** (2025)：
  - **评估维度**：建立多维度评估框架，系统评估LLM的分层推理能力
  - **结构化任务**：专门针对需要理解层次结构的任务设计
  - **ICL应用**：为分层示例选择提供了评估基准

### 工具学习中的示例选择
- **HiTEC框架** (2025)：
  - **多工具选择**：在工具学习场景中的示例选择策略
  - **层次化方法**：采用分层方法处理复杂的工具选择任务
  - **ICL集成**：将ICL与工具学习有机结合

### 表格数据生成中的示例选择
- **TABGEN-ICL** (ACL 2025 Findings)：
  - **残差感知选择**：针对表格数据生成任务的残差感知示例选择
  - **任务特定优化**：专门为结构化数据生成优化的选择策略
  - **性能验证**：在表格数据生成任务上验证有效性

### 标签引导的上下文学习
- **Bai et al. (2025)** - Label-Guided In-Context Learning for Named Entity Recognition
  - **发表信息**：arXiv:2505.23722，2025年5月29日提交
  - **核心创新**：提出标签引导的ICL方法，专门针对NER任务优化
  - **DEER方法**：开发了一种新的示例选择策略，利用标签信息指导选择过程
  - **技术特点**：
    - 不仅考虑文本相似性，还考虑标签分布的匹配
    - 通过标签统计信息优化示例选择
    - 避免标签不平衡对示例选择的负面影响
  - **实验验证**：在多个NER基准数据集上验证方法有效性
  - **开源贡献**：提供了完整的代码实现，促进研究复现

### 关键洞察与趋势

#### 1. 从通用到任务特定
- **趋势转变**：从通用的语义相似度转向任务特定的表示学习
- **技术路径**：学习任务相关的嵌入空间，而非依赖预训练的通用嵌入
- **性能提升**：任务特定的选择策略普遍优于通用方法

#### 2. 思维链有效性的边界
- **理论认知**：CoT的有效性存在边界条件，不是万能的性能提升方案
- **实践指导**：高质量示例可能比复杂的推理指导更重要
- **成本考量**：需要平衡性能提升与计算成本

#### 3. 多维度评估的重要性
- **评估体系**：建立更全面的多维度评估框架
- **结构化任务**：特别关注需要理解层次结构的任务
- **基准建设**：为不同类型的ICL任务建立专门的评估基准

#### 4. 跨领域应用扩展
- **应用范围**：ICL示例选择策略扩展到工具学习、表格生成等新领域
- **方法迁移**：NER中的多维度选择思想被应用到其他结构化任务
- **技术融合**：ICL与其他技术(如工具学习)的深度融合

---

*本知识库基于references.bib中的文献整理，涵盖2003-2025年间的主要NER研究进展，最后更新：2025年1月*
</file>

<file path="references.bib">
@misc{adiga_designing_2024,
  title      = {Designing Informative Metrics for Few-Shot Example Selection},
  url        = {http://arxiv.org/abs/2403.03861},
  doi        = {10.48550/arXiv.2403.03861},
  abstract   = {Pretrained language models ({PLMs}) have shown remarkable few-shot learning capabilities when provided with properly formatted examples. However, selecting the "best" examples remains an open challenge. We propose a complexity-based prompt selection approach for sequence tagging tasks. This approach avoids the training of a dedicated model for selection of examples, and instead uses certain metrics to align the syntactico-semantic complexity of test sentences and examples. We use both sentence- and word-level metrics to match the complexity of examples to the (test) sentence being considered. Our results demonstrate that our approach extracts greater performance from {PLMs}: it achieves state-of-the-art performance on few-shot {NER}, achieving a 5\% absolute improvement in F1 score on the {CoNLL}2003 dataset for {GPT}-4. We also see large gains of upto 28.85 points (F1/Acc.) in smaller models like {GPT}-j-6B.},
  number     = {{arXiv}:2403.03861},
  publisher  = {{arXiv}},
  author     = {Adiga, Rishabh and Subramanian, Lakshminarayanan and Chandrasekaran, Varun},
  urldate    = {2025-06-20},
  year       = {2024-07-30},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2403.03861 [cs]},
  keywords   = {Computer Science - Computation and Language, Computer Science - Machine Learning},
  file       = {PDF:files/536/Adiga 等 - 2024 - Designing Informative Metrics for Few-Shot Example Selection.pdf:application/pdf}
}

@misc{ashok_promptner_2023,
  title      = {{PromptNER}: Prompting For Named Entity Recognition},
  url        = {http://arxiv.org/abs/2305.15444},
  doi        = {10.48550/arXiv.2305.15444},
  shorttitle = {{PromptNER}},
  abstract   = {In a surprising turn, Large Language Models ({LLMs}) together with a growing arsenal of prompt-based heuristics now offer powerful off-the-shelf approaches providing few-shot solutions to myriad classic {NLP} problems. However, despite promising early results, these {LLM}-based few-shot methods remain far from the state of the art in Named Entity Recognition ({NER}), where prevailing methods include learning representations via end-to-end structural understanding and fine-tuning on standard labeled corpora. In this paper, we introduce {PromptNER} , a new state-of-the-art algorithm for few-Shot and cross-domain {NER}. To adapt to any new {NER} task {PromptNER} requires a set of entity definitions in addition to the standard few-shot examples. Given a sentence, {PromptNER} prompts an {LLM} to produce a list of potential entities along with corresponding explanations justifying their compatibility with the provided entity type definitions. {PromptNER} achieves state-of-the-art performance on fewshot {NER}, achieving a 4\% (absolute) improvement in F1 score on the {ConLL} dataset, a 9\% (absolute) improvement on the {GENIA} dataset, and a 4\% (absolute) improvement on the {FewNERD} dataset. {PromptNER} also moves the state of the art on Cross Domain {NER}, outperforming prior methods (including those not limited to the few-shot setting), setting a new mark on 3/5 {CrossNER} target domains, with an average F1 gain of 3\%, despite using less than 2\% of the available data.},
  number     = {{arXiv}:2305.15444},
  publisher  = {{arXiv}},
  author     = {Ashok, Dhananjay and Lipton, Zachary C.},
  urldate    = {2025-06-20},
  year       = {2023-06-20},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2305.15444 [cs]},
  keywords   = {Computer Science - Artificial Intelligence, Computer Science - Computation and Language, Computer Science - Machine Learning},
  annotation = {
                
                该论文认为，传统的少样本（Few-Shot）{ICL方法在NER上表现不佳}，是因为仅提供示例不足以让{LLM理解特定领域中}“什么才算是一个实体”。因此，他们提出 {PromptNER}，其核心是在Prompt中除了提供少量示例外，还必须提供一个明确的、模块化的实体类型定义（entity definitions） 。
                
                
                
                主要方法:
                
                
                模块化定义 (Modular Definitions): 为每个{NER任务创建一个自然语言的定义文档}，详细描述每个实体类型应该包含什么、不应该包含什么 。这个定义可以灵活替换，以适应不同领域 。
                
                
                链式思考式输出 (Chain-of-Thought like Output): {Prompt不仅要求LLM识别实体}，还要求它对每个候选实体做出判断（是/否为实体），并提供一句话的**解释（explanation）**来证明其判断的合理性 。这种结构化的输出格式，促使模型进行更深入的思考。
                
                
                Seq2Seq范式: 将{NER任务建模为序列到序列的生成任务}，而不是传统的序列标注任务，这样能更好地处理多词实体，并与{CoT等推理方法结合} 。
                
                
                
                
                
                
                
                },
  file       = {PDF:files/531/Ashok和Lipton - 2023 - PromptNER Prompting For Named Entity Recognition.pdf:application/pdf}
}

@inproceedings{babych_improving_2003,
  title      = {Improving Machine Translation Quality with Automatic Named Entity Recognition},
  url        = {https://aclanthology.org/W03-2201/},
  eventtitle = {{EAMT} 2003},
  booktitle  = {Proceedings of the 7th International {EAMT} workshop on {MT} and other language technology tools, Improving {MT} through other language technology tools, Resource and tools for building {MT} at {EACL} 2003},
  author     = {Babych, Bogdan and Hartley, Anthony},
  urldate    = {2025-06-21},
  year       = {2003},
  annotation = {机器翻译
                
                },
  file       = {Full Text PDF:files/643/Babych和Hartley - 2003 - Improving Machine Translation Quality with Automatic Named Entity Recognition.pdf:application/pdf}
}

@misc{brown2020languagemodelsfewshotlearners,
  title         = {Language Models are Few-Shot Learners},
  author        = {Tom B. Brown and Benjamin Mann and Nick Ryder and Melanie Subbiah and Jared Kaplan and Prafulla Dhariwal and Arvind Neelakantan and Pranav Shyam and Girish Sastry and Amanda Askell and Sandhini Agarwal and Ariel Herbert-Voss and Gretchen Krueger and Tom Henighan and Rewon Child and Aditya Ramesh and Daniel M. Ziegler and Jeffrey Wu and Clemens Winter and Christopher Hesse and Mark Chen and Eric Sigler and Mateusz Litwin and Scott Gray and Benjamin Chess and Jack Clark and Christopher Berner and Sam McCandlish and Alec Radford and Ilya Sutskever and Dario Amodei},
  year          = {2020},
  eprint        = {2005.14165},
  archiveprefix = {arXiv},
  primaryclass  = {cs.CL},
  url           = {https://arxiv.org/abs/2005.14165}
}

@misc{chen_learning_2023,
  title      = {Learning In-context Learning for Named Entity Recognition},
  url        = {http://arxiv.org/abs/2305.11038},
  doi        = {10.48550/arXiv.2305.11038},
  abstract   = {Named entity recognition in real-world applications suffers from the diversity of entity types, the emergence of new entity types, and the lack of high-quality annotations. To address the above problems, this paper proposes an in-context learning-based {NER} approach, which can effectively inject in-context {NER} ability into {PLMs} and recognize entities of novel types on-the-fly using only a few demonstrative instances. Specifically, we model {PLMs} as a meta-function λinstruction, demonstrations, text.M1, and a new entity extractor can be implicitly constructed by applying new instruction and demonstrations to {PLMs}, i.e., (λ.M)(instruction, demonstrations) → F where F will be a new entity extractor, i.e., F: text → entities. To inject the above in-context {NER} ability into {PLMs}, we propose a meta-function pre-training algorithm, which pre-trains {PLMs} by comparing the (instruction, demonstration)-initialized extractor with a surrogate golden extractor. Experimental results on 4 few-shot {NER} datasets show that our method can effectively inject in-context {NER} ability into {PLMs} and significantly outperforms the {PLMs}+fine-tuning counterparts.},
  number     = {{arXiv}:2305.11038},
  publisher  = {{arXiv}},
  author     = {Chen, Jiawei and Lu, Yaojie and Lin, Hongyu and Lou, Jie and Jia, Wei and Dai, Dai and Wu, Hua and Cao, Boxi and Han, Xianpei and Sun, Le},
  urldate    = {2025-06-20},
  year       = {2023-05-26},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2305.11038 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {Comment: Accepted to {ACL} 2023 Main Conference},
  annotation = {Comment: Accepted to {ACL} 2023 Main Conference},
  file       = {PDF:files/535/Chen 等 - 2023 - Learning In-context Learning for Named Entity Recognition.pdf:application/pdf}
}

@inproceedings{chen_self-icl_2023,
  location   = {Singapore},
  title      = {Self-{ICL}: Zero-Shot In-Context Learning with Self-Generated Demonstrations},
  url        = {https://aclanthology.org/2023.emnlp-main.968},
  doi        = {10.18653/v1/2023.emnlp-main.968},
  shorttitle = {Self-{ICL}},
  eventtitle = {Proceedings of the 2023 Conference on Empirical Methods in Natural Language Processing},
  pages      = {15651--15662},
  booktitle  = {Proceedings of the 2023 Conference on Empirical Methods in Natural Language Processing},
  publisher  = {Association for Computational Linguistics},
  author     = {Chen, Wei-Lin and Wu, Cheng-Kuang and Chen, Yun-Nung and Chen, Hsin-Hsi},
  urldate    = {2025-06-20},
  year       = {2023},
  langid     = {english},
  file       = {PDF:files/538/Chen 等 - 2023 - Self-ICL Zero-Shot In-Context Learning with Self-Generated Demonstrations.pdf:application/pdf}
}

@article{huang_guidener_nodate,
  title      = {{GuideNER}: Annotation Guidelines Are Better than Examples for In-Context Named Entity Recognition},
  abstract   = {Large language models ({LLMs}) demonstrate impressive performance on downstream tasks through in-context learning ({ICL}). However, there is a significant gap between their performance in Named Entity Recognition ({NER}) and in finetuning methods. We believe this discrepancy is due to inconsistencies in labeling definitions in {NER}. In addition, recent research indicates that {LLMs} do not learn the specific inputlabel mappings from the demonstrations. Therefore, we argue that using examples to implicitly capture the mapping between inputs and labels in in-context learning is not suitable for {NER}. Instead, it requires explicitly informing the model of the range of entities contained in the labels, such as annotation guidelines. In this paper, we propose {GuideNER}, which uses {LLMs} to summarize concise annotation guidelines as contextual information in {ICL}. We have conducted experiments on widely used {NER} datasets, and the experimental results indicate that our method can consistently and significantly outperform state-of-the-art methods, while using shorter prompts. Especially on the {GENIA} dataset, our model outperforms the previous state-of-the-art model by 12.63 F1 scores.},
  author     = {Huang, Shizhou and Xu, Bo and Yu, Yang and Li, Changqun and Lin, Xin Alex},
  langid     = {english},
  annotation = {
                
                对于{NER任务}，在上下文学习（In-Context Learning, {ICL}）中，直接给模型看几个输入输出的例子（examples），效果并不好。因为{LLM不一定能从有限的}、具体的例子中正确归纳出抽象的标注规则 。更有效的方法是，直接告诉模型明确的“标注指南”（Annotation Guidelines） 。
                
                
                
                主要方法 (三步法):
                
                
                总结模式 (Summarize Patterns): 使用{LLM从训练集的每个}（输入-标签）对中，总结出通用的模式或规则 。
                
                
                自我验证 (Self-Verify): 再次使用{LLM}，验证上一步总结出的规则是否能够准确地预测回原始的标签。过滤掉那些导致错误预测的、有歧义的规则 。
                
                
                聚合规则 (Aggregate Rules): 统计所有经过验证的准确规则的出现频率，筛选出最高频、最通用的Top-K条规则，聚合成最终给用户的“标注指南” 。
                
                
                
                
                关键发现:
                
                
                使用{LLM生成的简洁}“标注指南”作为上下文，性能显著优于使用一堆“示例”作为上下文的传统{ICL方法} 。
                
                
                该方法在多个数据集上达到{SOTA}（State-of-the-art）性能，尤其在{GENIA数据集上提升了}12.63{个F}1点，且Prompt长度更短 。
                
                
                
                
                },
  file       = {PDF:files/539/Huang 等 - GuideNER Annotation Guidelines Are Better than Examples for In-Context Named Entity Recognition.pdf:application/pdf}
}

@misc{ji_vicunaner_2023,
  title      = {{VicunaNER}: Zero/Few-shot Named Entity Recognition using Vicuna},
  url        = {http://arxiv.org/abs/2305.03253},
  doi        = {10.48550/arXiv.2305.03253},
  shorttitle = {{VicunaNER}},
  abstract   = {Large Language Models ({LLMs}, e.g., {ChatGPT}) have shown impressive zero- and fewshot capabilities in Named Entity Recognition ({NER}). However, these models can only be accessed via online {APIs}, which may cause data leak and non-reproducible problems. In this paper, we propose {VicunaNER}, a zero/fewshot {NER} framework based on the newly released open-source {LLM} – Vicuna. {VicunaNER} is a two-phase framework, where each phase leverages multi-turn dialogues with Vicuna to recognize entities from texts. We name the second phase as Re-Recognition, which recognizes those entities not recognized in the ﬁrst phase (a.k.a. Recongition). Moreover, we set entity correctness check dialogues in each phase to ﬁlter out wrong entities. We evaluate {VicunaNER}’s zero-shot capacity on 10 datasets crossing 5 domains and few-shot capacity on Few-{NERD}. Experimental results demonstrate that {VicunaNER} achieves superior performance in both shot settings. Additionally, we conduct comprehensive investigations on Vicuna from multiple perspectives.},
  number     = {{arXiv}:2305.03253},
  publisher  = {{arXiv}},
  author     = {Ji, Bin},
  urldate    = {2025-06-20},
  year       = {2023-05-05},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2305.03253 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                该论文基于开源模型Vicuna，提出了一个两阶段的{NER框架}，旨在解决单次识别（Recognition）中实体召回率不足的问题。
                
                
                主要方法:
                
                
                两阶段识别 (Two-phase Framework):
                
                
                阶段一：识别 (Recognition): 使用多轮对话进行初次实体识别，并包含一个“正确性检查”对话环节，用于过滤掉明显错误的实体 。
                
                
                阶段二：再识别 (Re-Recognition): 将第一阶段识别出的实体作为已知信息，再次Prompt模型，要求它专门去识别那些“在第一阶段被遗漏的”实体 。
                
                
                
                
                实体合并: 将两个阶段识别出的实体列表进行合并去重，得到最终结果 。
                
                
                
                
                },
  file       = {PDF:files/529/Ji - 2023 - VicunaNER ZeroFew-shot Named Entity Recognition using Vicuna.pdf:application/pdf}
}

@misc{jiang_p-icl_2024,
  title      = {P-{ICL}: Point In-Context Learning for Named Entity Recognition with Large Language Models},
  url        = {http://arxiv.org/abs/2405.04960},
  doi        = {10.48550/arXiv.2405.04960},
  shorttitle = {P-{ICL}},
  abstract   = {In recent years, the rise of large language models ({LLMs}) has made it possible to directly achieve named entity recognition ({NER}) without any demonstration samples or only using a few samples through in-context learning ({ICL}). However, standard {ICL} only helps {LLMs} understand task instructions, format and input-label mapping, but neglects the particularity of the {NER} task itself. In this paper, we propose a new prompting framework P-{ICL} to better achieve {NER} with {LLMs}, in which some point entities are leveraged as the auxiliary information to recognize each entity type. With such significant information, the {LLM} can achieve entity classification more precisely. To obtain optimal point entities for prompting {LLMs}, we also proposed a point entity selection method based on K-Means clustering. Our extensive experiments on some representative {NER} benchmarks verify the effectiveness of our proposed strategies in P-{ICL} and point entity selection.},
  number     = {{arXiv}:2405.04960},
  publisher  = {{arXiv}},
  author     = {Jiang, Guochao and Ding, Zepeng and Shi, Yuchen and Yang, Deqing},
  urldate    = {2025-06-20},
  year       = {2024-06-17},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2405.04960 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                标准{ICL提供的完整句子示例信息冗余}，且忽略了{NER任务的特殊性} 。该论文认为，直接为每个实体类型提供一组**代表性的实体词（Point Entities）**作为示例，比提供完整的句子更有效。
                
                
                
                主要方法:
                
                
                P-{ICL框架}: 在Prompt中，除了任务指令和少量few-shot句子示例外，为每个实体类型（如{PER}, {LOC}, {ORG}）明确列出一些典型的实体词（例如，{LOC}: London, China, California）。
                
                
                Point Entity选择: 为了选出有代表性的实体词，作者使用了K-Means聚类方法。他们先将训练集中所有同类实体用{BERT等模型编码为向量}，然后进行K-Means聚类，最后选取离每个簇中心最近的实体词作为“Point Entities” 。
                
                
                
                
                
                
                
                },
  file       = {PDF:files/563/Jiang 等 - 2024 - P-ICL Point In-Context Learning for Named Entity Recognition with Large Language Models.pdf:application/pdf}
}

@misc{keraghel_recent_2024,
  title      = {Recent Advances in Named Entity Recognition: A Comprehensive Survey and Comparative Study},
  url        = {http://arxiv.org/abs/2401.10825},
  doi        = {10.48550/arXiv.2401.10825},
  shorttitle = {Recent Advances in Named Entity Recognition},
  abstract   = {Named Entity Recognition seeks to extract substrings within a text that name real-world objects and to determine their type (for example, whether they refer to persons or organizations). In this survey, we first present an overview of recent popular approaches, including advancements in Transformer-based methods and Large Language Models ({LLMs}) that have not had much coverage in other surveys. In addition, we discuss reinforcement learning and graph-based approaches, highlighting their role in enhancing {NER} performance. Second, we focus on methods designed for datasets with scarce annotations. Third, we evaluate the performance of the main {NER} implementations on a variety of datasets with differing characteristics (as regards their domain, their size, and their number of classes). We thus provide a deep comparison of algorithms that have never been considered together. Our experiments shed some light on how the characteristics of datasets affect the behavior of the methods we compare.},
  number     = {{arXiv}:2401.10825},
  publisher  = {{arXiv}},
  author     = {Keraghel, Imed and Morbieu, Stanislas and Nadif, Mohamed},
  urldate    = {2025-06-20},
  year       = {2024-12-20},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2401.10825 [cs]},
  keywords   = {Computer Science - Computation and Language, Computer Science - Machine Learning},
  annotation = {Comment: 42 pages},
  file       = {PDF:files/572/Keraghel 等 - 2024 - Recent Advances in Named Entity Recognition A Comprehensive Survey and Comparative Study.pdf:application/pdf}
}

@misc{keraghel_recent_2024-1,
  title      = {Recent Advances in Named Entity Recognition: A Comprehensive Survey and Comparative Study},
  url        = {http://arxiv.org/abs/2401.10825},
  doi        = {10.48550/arXiv.2401.10825},
  shorttitle = {Recent Advances in Named Entity Recognition},
  abstract   = {Named Entity Recognition seeks to extract substrings within a text that name real-world objects and to determine their type (for example, whether they refer to persons or organizations). In this survey, we first present an overview of recent popular approaches, including advancements in Transformer-based methods and Large Language Models ({LLMs}) that have not had much coverage in other surveys. In addition, we discuss reinforcement learning and graph-based approaches, highlighting their role in enhancing {NER} performance. Second, we focus on methods designed for datasets with scarce annotations. Third, we evaluate the performance of the main {NER} implementations on a variety of datasets with differing characteristics (as regards their domain, their size, and their number of classes). We thus provide a deep comparison of algorithms that have never been considered together. Our experiments shed some light on how the characteristics of datasets affect the behavior of the methods we compare.},
  number     = {{arXiv}:2401.10825},
  publisher  = {{arXiv}},
  author     = {Keraghel, Imed and Morbieu, Stanislas and Nadif, Mohamed},
  urldate    = {2025-06-21},
  year       = {2024-12-20},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2401.10825 [cs]},
  keywords   = {Computer Science - Computation and Language, Computer Science - Machine Learning},
  annotation = {Comment: 42 pages},
  file       = {PDF:files/639/Keraghel 等 - 2024 - Recent Advances in Named Entity Recognition A Comprehensive Survey and Comparative Study.pdf:application/pdf}
}

@misc{lee_good_2022,
  title      = {Good Examples Make A Faster Learner: Simple Demonstration-based Learning for Low-resource {NER}},
  url        = {http://arxiv.org/abs/2110.08454},
  doi        = {10.48550/arXiv.2110.08454},
  shorttitle = {Good Examples Make A Faster Learner},
  abstract   = {Recent advances in prompt-based learning have shown strong results on few-shot text classification by using cloze-style templates. Similar attempts have been made on named entity recognition ({NER}) which manually design templates to predict entity types for every text span in a sentence. However, such methods may suffer from error propagation induced by entity span detection, high cost due to enumeration of all possible text spans, and omission of inter-dependencies among token labels in a sentence. Here we present a simple demonstration-based learning method for {NER}, which lets the input be prefaced by task demonstrations for in-context learning. We perform a systematic study on demonstration strategy regarding what to include (entity examples, with or without surrounding context), how to select the examples, and what templates to use. Results on in-domain learning and domain adaptation show that the model's performance in low-resource settings can be largely improved with a suitable demonstration strategy (e.g., a 4-17\% improvement on 25 train instances). We also find that good demonstration can save many labeled examples and consistency in demonstration contributes to better performance.},
  number     = {{arXiv}:2110.08454},
  publisher  = {{arXiv}},
  author     = {Lee, Dong-Ho and Kadakia, Akshen and Tan, Kangmin and Agarwal, Mahak and Feng, Xinyu and Shibuya, Takashi and Mitani, Ryosuke and Sekiya, Toshiyuki and Pujara, Jay and Ren, Xiang},
  urldate    = {2025-06-20},
  year       = {2022-03-31},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2110.08454 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {Comment: Accepted to {ACL} 2022 main conference. 14 pages, 8 figures, 9 tables},
  file       = {PDF:files/567/Lee 等 - 2022 - Good Examples Make A Faster Learner Simple Demonstration-based Learning for Low-resource NER.pdf:application/pdf}
}

@misc{li_rethinking_2022,
  title      = {Rethinking Negative Sampling for Handling Missing Entity Annotations},
  url        = {http://arxiv.org/abs/2108.11607},
  doi        = {10.48550/arXiv.2108.11607},
  abstract   = {Negative sampling is highly effective in handling missing annotations for named entity recognition ({NER}). One of our contributions is an analysis on how it makes sense through introducing two insightful concepts: missampling and uncertainty. Empirical studies show low missampling rate and high uncertainty are both essential for achieving promising performances with negative sampling. Based on the sparsity of named entities, we also theoretically derive a lower bound for the probability of zero missampling rate, which is only relevant to sentence length. The other contribution is an adaptive and weighted sampling distribution that further improves negative sampling via our former analysis. Experiments on synthetic datasets and well-annotated datasets (e.g., {CoNLL}-2003) show that our proposed approach beneﬁts negative sampling in terms of F1 score and loss convergence. Besides, models with improved negative sampling have achieved new state-of-the-art results on realworld datasets (e.g., {EC}).},
  number     = {{arXiv}:2108.11607},
  publisher  = {{arXiv}},
  author     = {Li, Yangming and Liu, Lemao and Shi, Shuming},
  urldate    = {2025-06-20},
  year       = {2022-02-25},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2108.11607 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                这篇论文主要研究在训练数据标注不完整（存在大量“漏标”实体）的情况下，如何有效训练{NER模型}。它深入分析了“负采样”（Negative Sampling）技术为何有效 。
                
                
                
                主要方法与概念:
                
                
                负采样: 在训练时，不把所有未标注的文本片段都当作负样本，而是从中随机采样一小部分作为负样本。这可以有效避免模型被“漏标”的实体（本应是正样本，却被当作负样本）误导 。
                
                
                错采样率 (Missampling Rate): 定义了在采样的负样本中，不慎包含了“漏标”实体的比例。论文证明，低错采样率是性能的关键 。
                
                
                不确定性 (Uncertainty): 指出采样那些模型“最不确定”的、最难判断的负样本进行训练，比采样简单的负样本效果更好 。
                
                
                加权采样: 基于以上分析，提出了一种加权的、自适应的采样方法，优先采样那些“不确定性高”且“错采样率低”的负样本 。
                
                
                
                
                
                
                
                },
  annotation = {Comment: A long paper accepted by {ACL}-2022},
  annotation = {Comment: A long paper accepted by {ACL}-2022},
  file       = {PDF:files/534/Li 等 - 2022 - Rethinking Negative Sampling for Handling Missing Entity Annotations.pdf:application/pdf}
}

@misc{lu_unified_2022,
  title      = {Unified Structure Generation for Universal Information Extraction},
  url        = {http://arxiv.org/abs/2203.12277},
  doi        = {10.48550/arXiv.2203.12277},
  abstract   = {Information extraction suffers from its varying targets, heterogeneous structures, and demandspeciﬁc schemas. In this paper, we propose a uniﬁed text-to-structure generation framework, namely {UIE}, which can universally model different {IE} tasks, adaptively generate targeted structures, and collaboratively learn general {IE} abilities from different knowledge sources. Speciﬁcally, {UIE} uniformly encodes different extraction structures via a structured extraction language, adaptively generates target extractions via a schema-based prompt mechanism – structural schema instructor, and captures the common {IE} abilities via a large-scale pretrained text-to-structure model. Experiments show that {UIE} achieved the state-of-the-art performance on 4 {IE} tasks, 13 datasets, and on all supervised, low-resource, and few-shot settings for a wide range of entity, relation, event and sentiment extraction tasks and their uniﬁcation. These results veriﬁed the effectiveness, universality, and transferability of {UIE}1.},
  number     = {{arXiv}:2203.12277},
  publisher  = {{arXiv}},
  author     = {Lu, Yaojie and Liu, Qing and Dai, Dai and Xiao, Xinyan and Lin, Hongyu and Han, Xianpei and Sun, Le and Wu, Hua},
  urldate    = {2025-06-20},
  year       = {2022-03-23},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2203.12277 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {Comment: Accepted to the main conference of {ACL}2022},
  annotation = {Comment: Accepted to the main conference of {ACL}2022},
  annotation = {Comment: Accepted to the main conference of {ACL}2022},
  annotation = {Comment: Accepted to the main conference of {ACL}2022},
  file       = {PDF:files/528/Lu 等 - 2022 - Unified Structure Generation for Universal Information Extraction.pdf:application/pdf}
}

@misc{meng_query-based_2019,
  title      = {Query-Based Named Entity Recognition},
  url        = {http://arxiv.org/abs/1908.09138},
  doi        = {10.48550/arXiv.1908.09138},
  abstract   = {In this paper, we propose a new strategy for the task of named entity recognition ({NER}). We cast the task as a query-based machine reading comprehension task: e.g., the task of extracting entities with {PER} is formalized as answering the question of "which person is mentioned in the text ?". Such a strategy comes with the advantage that it solves the long-standing issue of handling overlapping or nested entities (the same token that participates in more than one entity categories) with sequence-labeling techniques for {NER}. Additionally, since the query encodes informative prior knowledge, this strategy facilitates the process of entity extraction, leading to better performances. We experiment the proposed model on five widely used {NER} datasets on English and Chinese, including {MSRA}, Resume, {OntoNotes}, {ACE}04 and {ACE}05. The proposed model sets new {SOTA} results on all of these datasets.},
  number     = {{arXiv}:1908.09138},
  publisher  = {{arXiv}},
  author     = {Meng, Yuxian and Li, Xiaoya and Sun, Zijun and Li, Jiwei},
  urldate    = {2025-06-21},
  year       = {2019-11-02},
  eprinttype = {arxiv},
  eprint     = {1908.09138 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {信息检索
                
                },
  annotation = {Comment: Please refer to the full version of this paper: A unified framework for named entity recognition {arXiv}:1910.11476},
  file       = {Preprint PDF:files/650/Meng 等 - 2019 - Query-Based Named Entity Recognition.pdf:application/pdf;Snapshot:files/651/1908.html:text/html}
}

@misc{min_rethinking_2022,
  title      = {Rethinking the Role of Demonstrations: What Makes In-Context Learning Work?},
  url        = {http://arxiv.org/abs/2202.12837},
  doi        = {10.48550/arXiv.2202.12837},
  shorttitle = {Rethinking the Role of Demonstrations},
  abstract   = {Large language models ({LMs}) are able to incontext learn—perform a new task via inference alone by conditioning on a few inputlabel pairs (demonstrations) and making predictions for new inputs. However, there has been little understanding of how the model learns and which aspects of the demonstrations contribute to end task performance. In this paper, we show that ground truth demonstrations are in fact not required—randomly replacing labels in the demonstrations barely hurts performance on a range of classiﬁcation and multi-choce tasks, consistently over 12 different models including {GPT}-3. Instead, we ﬁnd that other aspects of the demonstrations are the key drivers of end task performance, including the fact that they provide a few examples of (1) the label space, (2) the distribution of the input text, and (3) the overall format of the sequence. Together, our analysis provides a new way of understanding how and why in-context learning works, while opening up new questions about how much can be learned from large language models through inference alone.},
  number     = {{arXiv}:2202.12837},
  publisher  = {{arXiv}},
  author     = {Min, Sewon and Lyu, Xinxi and Holtzman, Ari and Artetxe, Mikel and Lewis, Mike and Hajishirzi, Hannaneh and Zettlemoyer, Luke},
  urldate    = {2025-06-20},
  year       = {2022-10-20},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2202.12837 [cs]},
  keywords   = {Computer Science - Artificial Intelligence, Computer Science - Computation and Language},
  annotation = {Comment: 17 pages; 12 figures. Published as a conference paper at {EMNLP} 2022 (long). Code available at https://github.com/Alrope123/rethinking-demonstrations},
  file       = {PDF:files/566/Min 等 - 2022 - Rethinking the Role of Demonstrations What Makes In-Context Learning Work.pdf:application/pdf}
}

@misc{mo_c-icl_2024,
  title      = {C-{ICL}: Contrastive In-context Learning for Information Extraction},
  url        = {http://arxiv.org/abs/2402.11254},
  doi        = {10.48550/arXiv.2402.11254},
  shorttitle = {C-{ICL}},
  abstract   = {There has been increasing interest in exploring the capabilities of advanced large language models ({LLMs}) in the field of information extraction ({IE}), specifically focusing on tasks related to named entity recognition ({NER}) and relation extraction ({RE}). Although researchers are exploring the use of few-shot information extraction through in-context learning with {LLMs}, they tend to focus only on using correct or positive examples for demonstration, neglecting the potential value of incorporating incorrect or negative examples into the learning process. In this paper, we present C-{ICL}, a novel few-shot technique that leverages both correct and incorrect sample constructions to create in-context learning demonstrations. This approach enhances the ability of {LLMs} to extract entities and relations by utilizing prompts that incorporate not only the positive samples but also the reasoning behind them. This method allows for the identification and correction of potential interface errors. Specifically, our proposed method taps into the inherent contextual information and valuable information in hard negative samples and the nearest positive neighbors to the test and then applies the in-context learning demonstrations based on {LLMs}. Our experiments on various datasets indicate that C-{ICL} outperforms previous few-shot in-context learning methods, delivering substantial enhancements in performance across a broad spectrum of related tasks. These improvements are noteworthy, showcasing the versatility of our approach in miscellaneous scenarios.},
  number     = {{arXiv}:2402.11254},
  publisher  = {{arXiv}},
  author     = {Mo, Ying and Liu, Jiahao and Yang, Jian and Wang, Qifan and Zhang, Shun and Wang, Jingang and Li, Zhoujun},
  urldate    = {2025-06-20},
  year       = {2024-06-24},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2402.11254 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {Comment: 15 pages},
  annotation = {Comment: 15 pages},
  file       = {PDF:files/540/Mo 等 - 2024 - C-ICL Contrastive In-context Learning for Information Extraction.pdf:application/pdf}
}

@article{molla_named_nodate,
  title      = {Named Entity Recognition for Question Answering},
  abstract   = {Current text-based question answering ({QA}) systems usually contain a named entity recogniser ({NER}) as a core component. Named entity recognition has traditionally been developed as a component for information extraction systems, and current techniques are focused on this end use. However, no formal assessment has been done on the characteristics of a {NER} within the task of question answering. In this paper we present a {NER} that aims at higher recall by allowing multiple entity labels to strings. The {NER} is embedded in a question answering system and the overall {QA} system performance is compared to that of one with a traditional variation of the {NER} that only allows single entity labels. It is shown that the added noise produced introduced by the additional labels is offset by the higher recall gained, therefore enabling the {QA} system to have a better chance to ﬁnd the answer.},
  author     = {Molla, Diego and van Zaanen, Menno and Smith, Daniel},
  langid     = {english},
  annotation = {qa
                },
  year       = {2006},
  file       = {PDF:files/645/Molla 等 - Named Entity Recognition for Question Answering.pdf:application/pdf}
}

@online{noauthor_231009881v4_nodate,
  title   = {[2310.09881v4] In-Context Learning with Iterative Demonstration Selection},
  url     = {https://arxiv.org/abs/2310.09881v4},
  urldate = {2025-06-20},
  file    = {[2310.09881v4] In-Context Learning with Iterative Demonstration Selection:files/521/2310.html:text/html}
}

@misc{peng_metaie_2024,
  title      = {{MetaIE}: Distilling a Meta Model from {LLM} for All Kinds of Information Extraction Tasks},
  url        = {http://arxiv.org/abs/2404.00457},
  doi        = {10.48550/arXiv.2404.00457},
  shorttitle = {{MetaIE}},
  abstract   = {Information extraction ({IE}) is a fundamental area in natural language processing where prompting large language models ({LLMs}), even with in-context examples, cannot defeat small {LMs} tuned on very small {IE} datasets. We observe that {IE} tasks, such as named entity recognition and relation extraction, all focus on extracting important information, which can be formalized as a label-to-span matching. In this paper, we propose a novel framework {MetaIE} to build a small {LM} as meta-model by learning to extract "important information", i.e., the meta-understanding of {IE}, so that this meta-model can be adapted to all kind of {IE} tasks effectively and efficiently. Specifically, {MetaIE} obtains the small {LM} via a symbolic distillation from an {LLM} following the label-to-span scheme. We construct the distillation dataset via sampling sentences from language model pre-training datasets (e.g., {OpenWebText} in our implementation) and prompting an {LLM} to identify the typed spans of "important information". We evaluate the meta-model under the few-shot adaptation setting. Extensive results on 13 datasets from 6 {IE} tasks confirm that {MetaIE} can offer a better starting point for few-shot tuning on {IE} datasets and outperform other meta-models from (1) vanilla language model pre-training, (2) multi-{IE}-task pre-training with human annotations, and (3) single-{IE}-task symbolic distillation from {LLM}. Moreover, we provide comprehensive analyses of {MetaIE}, such as the size of the distillation dataset, the meta-model architecture, and the size of the meta-model.},
  number     = {{arXiv}:2404.00457},
  publisher  = {{arXiv}},
  author     = {Peng, Letian and Wang, Zilong and Yao, Feng and Wang, Zihan and Shang, Jingbo},
  urldate    = {2025-06-20},
  year       = {2024-03-30},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2404.00457 [cs]},
  keywords   = {Computer Science - Computation and Language},
  file       = {PDF:files/562/Peng 等 - 2024 - MetaIE Distilling a Meta Model from LLM for All Kinds of Information Extraction Tasks.pdf:application/pdf}
}

@misc{qin_-context_2024,
  title      = {In-Context Learning with Iterative Demonstration Selection},
  url        = {http://arxiv.org/abs/2310.09881},
  doi        = {10.48550/arXiv.2310.09881},
  abstract   = {Spurred by advancements in scale, large language models ({LLMs}) have demonstrated strong few-shot learning ability via in-context learning ({ICL}). However, the performance of {ICL} has been shown to be highly sensitive to the selection of few-shot demonstrations. Selecting the most suitable examples as context remains an ongoing challenge and an open problem. Existing literature has highlighted the importance of selecting examples that are diverse or semantically similar to the test sample while ignoring the fact that the optimal selection dimension, i.e., diversity or similarity, is task-specific. Based on how the test sample is answered, we propose Iterative Demonstration Selection ({IDS}) to leverage the merits of both dimensions. Using zero-shot chain-of-thought reasoning (Zero-shot-{CoT}), {IDS} iteratively selects examples that are diverse but still strongly correlated with the test sample as {ICL} demonstrations. Specifically, {IDS} applies Zero-{shotCoT} to the test sample before demonstration selection. The output reasoning path is then used to choose demonstrations that are prepended to the test sample for inference. The generated answer is followed by its corresponding reasoning path for extracting a new set of demonstrations in the next iteration. After several iterations, {IDS} adopts majority voting to obtain the final result. Through extensive experiments on tasks including reasoning, question answering, and topic classification, we demonstrate that {IDS} can consistently outperform existing {ICL} demonstration selection methods.},
  number     = {{arXiv}:2310.09881},
  publisher  = {{arXiv}},
  author     = {Qin, Chengwei and Zhang, Aston and Chen, Chen and Dagar, Anirudh and Ye, Wenming},
  urldate    = {2025-06-20},
  year       = {2024-12-30},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2310.09881 [cs]},
  keywords   = {Computer Science - Artificial Intelligence, Computer Science - Computation and Language},
  annotation = {
                
                该论文指出，{ICL演示}（demonstrations）的最佳选择标准（是追求“多样性”还是“相似性”）是依任务而定的 。为此，它提出了一种**迭代式演示选择（{IDS}）**方法，试图结合两者的优点。
                
                
                
                主要方法:
                
                
                从Zero-shot-{CoT开始}: 对于一个测试样本，不先选演示，而是先用Zero-shot-{CoT}（例如，在末尾加上“Let's think step by step.”）让{LLM生成一个初步的推理路径} 。
                
                
                用推理路径检索: 将这个生成的推理路径作为Query，去训练集中检索与之语义最相似的k个样本作为演示 。
                
                
                迭代优化: 用上一步选出的演示和原始样本再次提问{LLM}，生成新的答案和新的推理路径。然后，用这个新的推理路径重复第2步，进行下一轮的演示选择 。
                
                
                投票得出结果: 迭代数次后，对每次生成的答案进行多数投票，得到最终结果 。
                
                
                
                
                },
  file       = {PDF:files/526/Qin 等 - 2024 - In-Context Learning with Iterative Demonstration Selection.pdf:application/pdf}
}

@misc{rodrigues_meta-prompting_2024,
  title      = {Meta-prompting Optimized Retrieval-augmented Generation},
  url        = {http://arxiv.org/abs/2407.03955},
  doi        = {10.48550/arXiv.2407.03955},
  abstract   = {Retrieval-augmented generation resorts to content retrieved from external sources in order to leverage the performance of large language models in downstream tasks. The excessive volume of retrieved content, the possible dispersion of its parts, or their out of focus range may happen nevertheless to eventually have a detrimental rather than an incremental eﬀect. To mitigate this issue and improve retrieval-augmented generation, we propose a method to reﬁne the retrieved content before it is included in the prompt by resorting to meta-prompting optimization. Put to empirical test with the demanding multi-hop question answering task from the {StrategyQA} dataset, the evaluation results indicate that this method outperforms a similar retrieval-augmented system but without this method by over 30 \%.},
  number     = {{arXiv}:2407.03955},
  publisher  = {{arXiv}},
  author     = {Rodrigues, João and Branco, António},
  urldate    = {2025-06-20},
  year       = {2024-07-04},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2407.03955 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                这篇论文关注如何优化检索增强生成（{RAG}）的性能。它认为从外部检索到的内容可能冗长、杂乱，直接拼接到Prompt中效果不佳 。因此，需要一个步骤来**“精炼”（refine）**这些检索到的内容。而精炼内容所用的“指令”本身，也可以通过{LLM进行自动优化}。
                
                
                
                主要方法:
                
                
                三{LLM架构}:
                
                
                生成{LLM} (generation-{LLM}): 执行最终的问答任务。
                
                
                转换{LLM} (transformation-{LLM}): 负责接收检索到的原始文档和一条“精炼指令”，然后输出精炼后的内容（如摘要、核心观点等）。
                
                
                优化器{LLM} (optimizer-{LLM}): 它的任务是生成并优化给转换{LLM的那条}“精炼指令”。
                
                
                
                
                元提示（Meta-prompting）: 优化器{LLM通过一个}“元提示”来工作。这个元提示里包含了“请你生成一条更好的精炼指令”的元指令，以及一个当前最优的精炼指令列表（附带它们历史表现的分数）。优化器{LLM会参考这些信息}，生成新的、可能更好的精炼指令。这个过程可以迭代进行，不断发现最优的精炼指令 。
                
                
                
                
                },
  file       = {PDF:files/530/Rodrigues和Branco - 2024 - Meta-prompting Optimized Retrieval-augmented Generation.pdf:application/pdf}
}

@misc{villena_llmner_2024,
  title      = {{llmNER}: (Zero{\textbar}Few)-Shot Named Entity Recognition, Exploiting the Power of Large Language Models},
  url        = {http://arxiv.org/abs/2406.04528},
  doi        = {10.48550/arXiv.2406.04528},
  shorttitle = {{llmNER}},
  abstract   = {Large language models ({LLMs}) allow us to generate high-quality human-like text. One interesting task in natural language processing ({NLP}) is named entity recognition ({NER}), which seeks to detect mentions of relevant information in documents. This paper presents {llmNER}, a Python library for implementing zero-shot and few-shot {NER} with {LLMs}; by providing an easyto-use interface, {llmNER} can compose prompts, query the model, and parse the completion returned by the {LLM}. Also, the library enables the user to perform prompt engineering efficiently by providing a simple interface to test multiple variables. We validated our software on two {NER} tasks to show the library’s flexibility. {llmNER} aims to push the boundaries of in-context learning research by removing the barrier of the prompting and parsing steps.},
  number     = {{arXiv}:2406.04528},
  publisher  = {{arXiv}},
  author     = {Villena, Fabián and Miranda, Luis and Aracena, Claudio},
  urldate    = {2025-06-20},
  year       = {2024-06-06},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2406.04528 [cs]},
  keywords   = {Computer Science - Computation and Language},
  file       = {PDF:files/571/Villena 等 - 2024 - llmNER (ZeroFew)-Shot Named Entity Recognition, Exploiting the Power of Large Language Models.pdf:application/pdf}
}

@misc{wang_cooperative_2025,
  title      = {A Cooperative Multi-Agent Framework for Zero-Shot Named Entity Recognition},
  url        = {http://arxiv.org/abs/2502.18702},
  doi        = {10.48550/arXiv.2502.18702},
  abstract   = {Zero-shot named entity recognition ({NER}) aims to develop entity recognition systems from unannotated text corpora. This task presents substantial challenges due to minimal human intervention. Recent work has adapted large language models ({LLMs}) for zero-shot {NER} by crafting specialized prompt templates. It advances model self-learning abilities by incorporating self-annotated demonstrations. However, two important challenges persist: (i) Correlations between contexts surrounding entities are overlooked, leading to wrong type predictions or entity omissions. (ii) The indiscriminate use of task demonstrations, retrieved through shallow similarity-based strategies, severely misleads {LLMs} during inference. In this paper, we introduce the cooperative multi-agent system ({CMAS}), a novel framework for zero-shot {NER} that uses the collective intelligence of multiple agents to address the challenges outlined above. {CMAS} has four main agents: (i) a self-annotator, (ii) a type-related feature ({TRF}) extractor, (iii) a demonstration discriminator, and (iv) an overall predictor. To explicitly capture correlations between contexts surrounding entities, {CMAS} reformulates {NER} into two subtasks: recognizing named entities and identifying entity type-related features within the target sentence. To enable controllable utilization of demonstrations, a demonstration discriminator is established to incorporate the self-reflection mechanism, automatically evaluating helpfulness scores for the target sentence. Experimental results show that {CMAS} significantly improves zero-shot {NER} performance across six benchmarks, including both domain-specific and general-domain scenarios. Furthermore, {CMAS} demonstrates its effectiveness in few-shot settings and with various {LLM} backbones.},
  number     = {{arXiv}:2502.18702},
  publisher  = {{arXiv}},
  author     = {Wang, Zihan and Zhao, Ziqi and Lyu, Yougang and Chen, Zhumin and Rijke, Maarten de and Ren, Zhaochun},
  urldate    = {2025-06-20},
  year       = {2025-02-25},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2502.18702 [cs]},
  keywords   = {Computer Science - Computation and Language, Computer Science - Information Retrieval},
  annotation = {
                
                这篇论文将Zero-shot {NER任务构建为一个多智能体协作系统}（Cooperative Multi-Agent System, {CMAS}） 。它认为单个{LLM通过简单的Prompt难以解决复杂的NER问题}，因此设计了多个各司其职的Agent，通过它们的协作来提升整体性能 。
                
                
                
                主要方法 (四Agent协作):
                
                
                自标注器 (Self-annotator): 负责在无标签数据上进行初步的实体标注，创建一个伪标签数据集 。
                
                
                类型相关特征提取器 ({TRF} Extractor): 重新定义了{NER任务}，不仅要识别实体，还要识别与实体类型强相关的上下文特征（{TRF}），以解决上下文关联被忽略的问题 。
                
                
                示例判别器 (Demonstration Discriminator): 引入自反思（Self-reflection）机制，在推理时，它会评估检索到的每个示例对于当前测试样本的“有效性得分”，避免被不相关的示例误导 。
                
                
                总预测器 (Overall Predictor): 综合所有信息（{TRF}、示例的有效性得分等），做出最终的{NER预测} 。
                
                
                
                
                
                
                
                },
  annotation = {Comment: Accepted at {WWW} 2025},
  annotation = {Comment: Accepted at {WWW} 2025},
  file       = {PDF:files/532/Wang 等 - 2025 - A Cooperative Multi-Agent Framework for Zero-Shot Named Entity Recognition.pdf:application/pdf}
}

@misc{wang_s-prompts_2023,
  title      = {S-Prompts Learning with Pre-trained Transformers: An Occam's Razor for Domain Incremental Learning},
  url        = {http://arxiv.org/abs/2207.12819},
  doi        = {10.48550/arXiv.2207.12819},
  shorttitle = {S-Prompts Learning with Pre-trained Transformers},
  abstract   = {State-of-the-art deep neural networks are still struggling to address the catastrophic forgetting problem in continual learning. In this paper, we propose one simple paradigm (named as S-Prompting) and two concrete approaches to highly reduce the forgetting degree in one of the most typical continual learning scenarios, i.e., domain increment learning ({DIL}). The key idea of the paradigm is to learn prompts independently across domains with pre-trained transformers, avoiding the use of exemplars that commonly appear in conventional methods. This results in a win-win game where the prompting can achieve the best for each domain. The independent prompting across domains only requests one single cross-entropy loss for training and one simple K-{NN} operation as a domain identiﬁer for inference. The learning paradigm derives an image prompt learning approach and a novel language-image prompt learning approach. Owning an excellent scalability (0.03\% parameter increase per domain), the best of our approaches achieves a remarkable relative improvement (an average of about 30\%) over the best of the state-of-the-art exemplar-free methods for three standard {DIL} tasks, and even surpasses the best of them relatively by about 6\% in average when they use exemplars. Source code is available at https://github.com/iamwangyabin/S-Prompts.},
  number     = {{arXiv}:2207.12819},
  publisher  = {{arXiv}},
  author     = {Wang, Yabin and Huang, Zhiwu and Hong, Xiaopeng},
  urldate    = {2025-06-20},
  year       = {2023-03-18},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2207.12819 [cs]},
  keywords   = {Computer Science - Machine Learning, Computer Science - Computer Vision and Pattern Recognition},
  annotation = {Comment: Accepted to {NeurIPS} 2022},
  file       = {PDF:files/570/Wang 等 - 2023 - S-Prompts Learning with Pre-trained Transformers An Occam's Razor for Domain Incremental Learning.pdf:application/pdf}
}

@misc{wang2023gptnernamedentityrecognition,
  title         = {GPT-NER: Named Entity Recognition via Large Language Models},
  author        = {Shuhe Wang and Xiaofei Sun and Xiaoya Li and Rongbin Ouyang and Fei Wu and Tianwei Zhang and Jiwei Li and Guoyin Wang},
  year          = {2023},
  eprint        = {2304.10428},
  archiveprefix = {arXiv},
  primaryclass  = {cs.CL},
  url           = {https://arxiv.org/abs/2304.10428},
  annotation    = {GPT-NER 框架
                   为了解决上述问题，作者提出了 GPT-NER 框架。其核心思想是通过一系列巧妙的设计，将NER任务彻底改造成一个LLM擅长的文本生成任务。
                   
                   该框架主要包含以下几个关键部分：
                   
                   1. 任务形式转换 (Transforming the Task Format)
                   
                   创新点: 作者没有让LLM输出B-I-O标签或者JSON对象，而是设计了一种新颖的**“原文改写”**格式。
                   具体做法: 要求LLM生成与输入句子几乎完全相同的文本，但用**特殊的分隔符（@@ 和 ##）**将识别出的实体包裹起来。
                   例如，要从句子 Columbus is a city 中识别“地点”实体，LLM需要生成 @@Columbus## is a city。
                   优势: 这种方式极大地降低了LLM的生成难度。模型不需要学习复杂的对齐或生成全新的结构，只需在原文基础上“复制粘贴”并“添加标记”，从而能更好地适应任务。
                   2. 示例检索 (Few-shot Demonstration Retrieval)
                   
                   为了给LLM提供高质量的上下文示例（demonstrations），作者比较了三种检索策略：
                   随机检索 (Random Retrieval): 随机从训练集中挑选示例，效果最差。
                   句子级kNN检索 (Sentence-level kNN): 检索与输入句子语义最相似的句子作为示例。效果有提升，但可能找到的句子虽然意思相近，却不包含关键的实体信息。
                   实体级kNN检索 (Entity-level kNN): 这是作者推荐的方法。先用一个预训练好的NER模型粗略地识别出输入句子中的潜在实体，然后用这些实体的表示（embedding）去检索训练集中包含相似实体的句子。这种方法能找到与任务最相关的示例，效果最好。
                   3. 自我验证机制 (Self-verification Strategy)
                   
                   动机: 解决LLM在NER任务中常见的**“幻觉”或过度预测**问题（即把不是实体的内容也识别为实体）。
                   创新点: 设计了一个两阶段流程。在第一阶段用上述方法抽取实体后，进入第二阶段。
                   具体做法: 在第二阶段，系统会向LLM发起一个新的、验证性的提问。例如，如果第一阶段将"Hendrix"识别为了“地点”，第二阶段就会问：“在句子‘Rare Hendrix song sells for $17’中，‘Hendrix’是一个地点实体吗？请用‘是’或‘否’回答。”
                   优势: 通过这种“自问自答”的方式，LLM可以对自己的初步判断进行二次审查，有效过滤掉许多错误的识别结果，显著提升了准确率（Precision）。
                   实验与主要发现
                   性能卓越: GPT-NER在多个NER数据集（包括扁平NER和嵌套NER）上，取得了与全监督基线模型**相当（comparable）**的性能，这是首次有研究在使用LLM的ICL方法上达到如此高度。
                   低资源优势: 在训练数据极度稀少（few-shot/low-resource）的情况下，GPT-NER的性能显著优于传统的监督模型。例如，在只有8个训练样本时，GPT-NER的F1分数已达到约60，而监督模型几乎为0。}
}

@misc{xie_empirical_2023,
  title      = {Empirical Study of Zero-Shot {NER} with {ChatGPT}},
  url        = {http://arxiv.org/abs/2310.10035},
  doi        = {10.48550/arXiv.2310.10035},
  abstract   = {Large language models ({LLMs}) exhibited powerful capability in various natural language processing tasks. This work focuses on exploring {LLM} performance on zero-shot information extraction, with a focus on the {ChatGPT} and named entity recognition ({NER}) task. Inspired by the remarkable reasoning capability of {LLM} on symbolic and arithmetic reasoning, we adapt the prevalent reasoning methods to {NER} and propose reasoning strategies tailored for {NER}. First, we explore a decomposed question-answering paradigm by breaking down the {NER} task into simpler subproblems by labels. Second, we propose syntactic augmentation to stimulate the model's intermediate thinking in two ways: syntactic prompting, which encourages the model to analyze the syntactic structure itself, and tool augmentation, which provides the model with the syntactic information generated by a parsing tool. Besides, we adapt self-consistency to {NER} by proposing a two-stage majority voting strategy, which first votes for the most consistent mentions, then the most consistent types. The proposed methods achieve remarkable improvements for zero-shot {NER} across seven benchmarks, including Chinese and English datasets, and on both domain-specific and general-domain scenarios. In addition, we present a comprehensive analysis of the error types with suggestions for optimization directions. We also verify the effectiveness of the proposed methods on the few-shot setting and other {LLMs}.},
  number     = {{arXiv}:2310.10035},
  publisher  = {{arXiv}},
  author     = {Xie, Tingyu and Li, Qi and Zhang, Jian and Zhang, Yan and Liu, Zuozhu and Wang, Hongwei},
  urldate    = {2025-06-20},
  year       = {2023-10-16},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2310.10035 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                这篇论文系统地研究了如何激发{ChatGPT在零样本}（Zero-Shot）{NER任务中的}“推理能力”。它认为，不能简单地直接要求模型输出结果，而应该通过更精巧的提示工程（Prompting）来引导模型进行“思考” 。
                
                
                
                主要方法:
                
                
                分解式问答 (Decomposed-{QA}): 将一次性抽取所有实体类型的复杂任务，分解成一个多轮对话过程。每一轮只要求模型抽取一种实体类型，并将之前的问答作为上下文 。这降低了任务的复杂度。
                
                
                句法增强 (Syntactic Augmentation): 通过两种方式为模型提供句法信息，引导其思考 。
                
                
                句法提示 (Syntactic Prompting): 在Prompt中加入提示语，鼓励模型先自行分析句法结构，再进行识别 。
                
                
                工具增强 (Tool Augmentation): 直接使用外部解析工具（如Hanlp）生成词性标注、依存树等信息，并将其作为输入提供给模型 。
                
                
                
                
                两阶段多数投票 (Two-Stage Majority Voting): 针对自洽性（Self-Consistency）的一种优化策略。在采样多个答案后，第一阶段先对实体本身（mention）进行投票，第二阶段再对实体类型（type）进行投票 。
                
                
                
                
                },
  annotation = {Comment: Accepted to {EMNLP} 2023 (Main Conference)},
  annotation = {Comment: Accepted to {EMNLP} 2023 (Main Conference)},
  file       = {PDF:files/533/Xie 等 - 2023 - Empirical Study of Zero-Shot NER with ChatGPT.pdf:application/pdf}
}

@misc{xie_retrieval_2024,
  title      = {Retrieval Augmented Instruction Tuning for Open {NER} with Large Language Models},
  url        = {http://arxiv.org/abs/2406.17305},
  doi        = {10.48550/arXiv.2406.17305},
  abstract   = {The strong capability of large language models ({LLMs}) has been applied to information extraction ({IE}) through either retrieval augmented prompting or instruction tuning ({IT}). However, the best way to incorporate information with {LLMs} for {IE} remains an open question. In this paper, we explore Retrieval Augmented Instruction Tuning ({RA}-{IT}) for {IE}, focusing on the task of open named entity recognition ({NER}). Specifically, for each training sample, we retrieve semantically similar examples from the training dataset as the context and prepend them to the input of the original instruction. To evaluate our {RA}-{IT} approach more thoroughly, we construct a Chinese {IT} dataset for open {NER} and evaluate {RA}-{IT} in both English and Chinese scenarios. Experimental results verify the effectiveness of {RA}-{IT} across various data sizes and in both English and Chinese scenarios. We also conduct thorough studies to explore the impacts of various retrieval strategies in the proposed {RA}-{IT} framework. Code and data are available at: https://github.com/Emma1066/Retrieval-Augmented-{IT}-{OpenNER}},
  number     = {{arXiv}:2406.17305},
  publisher  = {{arXiv}},
  author     = {Xie, Tingyu and Zhang, Jian and Zhang, Yan and Liang, Yuanyuan and Li, Qi and Wang, Hongwei},
  urldate    = {2025-06-20},
  year       = {2024-12-01},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2406.17305 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                这篇论文的核心观点是，在对模型进行指令微调（Instruction Tuning）时，仅仅使用原始指令是不够的。如果在每个训练样本的指令前，先从训练集中**检索(Retrieval)**出语义相似的样本作为上下文，可以显著提升模型在开放域命名实体识别（Open {NER}）任务上的性能 。他们将这种方法称为 {RA}-{IT} (Retrieval Augmented - Instruction Tuning) 。
                
                
                
                主要方法:
                
                
                对于每一个训练样本，使用基于句子嵌入的检索器（如{GTE}-large）在整个训练数据集中找到k个语义最相似的样本 。
                
                
                将这k个检索到的样本（作为上下文）拼接到原始的指令微调模板之前 。
                
                
                使用这种“增强后”的数据对大语言模型（{LLaMA}-3, Qwen-1.5）进行微调 。
                
                
                
                
                },
  annotation = {Comment: To be appeared at {COLING} 2025},
  annotation = {Comment: To be appeared at {COLING} 2025},
  file       = {PDF:files/537/Xie 等 - 2024 - Retrieval Augmented Instruction Tuning for Open NER with Large Language Models.pdf:application/pdf}
}

@inproceedings{zhang-etal-2023-2iner,
  title      = {2{INER}: Instructive and In-Context Learning on Few-Shot Named Entity Recognition},
  author     = {Zhang, Jiasheng  and
                Liu, Xikai  and
                Lai, Xinyi  and
                Gao, Yan  and
                Wang, Shusen  and
                Hu, Yao  and
                Lin, Yiqing},
  editor     = {Bouamor, Houda  and
                Pino, Juan  and
                Bali, Kalika},
  booktitle  = {Findings of the Association for Computational Linguistics: EMNLP 2023},
  month      = dec,
  year       = {2023},
  address    = {Singapore},
  publisher  = {Association for Computational Linguistics},
  url        = {https://aclanthology.org/2023.findings-emnlp.259/},
  doi        = {10.18653/v1/2023.findings-emnlp.259},
  pages      = {3940--3951},
  abstract   = {Prompt-based learning has emerged as a powerful technique in natural language processing (NLP) due to its ability to leverage pre-training knowledge for downstream few-shot tasks. In this paper, we propose 2INER, a novel text-to-text framework for Few-Shot Named Entity Recognition (NER) tasks. Our approach employs instruction finetuning based on InstructionNER to enable the model to effectively comprehend and process task-specific instructions, including both main and auxiliary tasks. We also introduce a new auxiliary task, called Type Extracting, to enhance the model{'}s understanding of entity types in the overall semantic context of a sentence. To facilitate in-context learning, we concatenate examples to the input, enabling the model to learn from additional contextual information. Experimental results on four datasets demonstrate that our approach outperforms existing Few-Shot NER methods and remains competitive with state-of-the-art standard NER algorithms.},
  annotation = {该论文旨在解决少样本命名实体识别（Few-Shot NER） 任务中的挑战，即在只有少量标注数据的情况下有效识别实体 。作者认为，虽然基于指令微调（instruction finetuning）的方法如InstructionNER已经取得了进展，但仍存在两个可提升的空间：一是模型对实体类型在句子全局语义下的理解不够深入；二是未能利用上下文学习（In-Context Learning, ICL）中通过示例提供额外知识的优势 。
                为此，论文提出了一个名为 2INER 的新框架，其核心是将指令微调和上下文学习两种技术融合在一起，以充分发掘预训练语言模型（PLM）的潜力 。
                方法论详解：2INER的两大创新
                2INER框架建立在InstructionNER的文本到文本（text-to-text）范式之上 ，通过引入一个新的辅助任务和加入上下文示例来增强模型能力。
                1. 新的辅助任务：类型抽取 (Type Extraction)
                动机: 作者认为，一个完整的NER过程可以分解为三步：理解标签与句子语义的关系、识别实体边界（span recognition）、为边界标注类型（entity labeling）。InstructionNER的辅助任务主要关注后两步，而2INER则旨在补强第一步 。
                任务定义: “类型抽取”任务要求模型在不关注具体实体边界的情况下，直接判断出给定句子中包含了哪些实体类型 。例如，对于句子“are there any cheap 4 star restaurants near here”，模型需要输出“rating type exists in the sentence”、“location type exists in the sentence”等 。
                目的与效果: 这个任务迫使模型从句子的整体语义层面去理解实体类型的含义，而不是仅仅依赖于某个词或短语 。作者认为，加深了对标签语义的理解后，模型在识别实体边界和进行实体标注时的能力都会得到同步提升 。
                2. 融入上下文学习 (In-Context Learning)
                动机: 利用ICL的优势，为模型提供额外的上下文信息以增强其少样本学习能力 。
                实施方式:
                在主NER任务的输入提示（prompt）中，除了任务指令、待处理句子和候选标签列表外，还拼接了一个“示例（Examples）”字段 。
                这些示例以自然语言的形式呈现，例如“‘modills’ is a restaurant name”，直观地向模型展示了哪种文本片段对应哪种实体类型 。
                为了避免信息泄露，推理时使用的示例从训练支持集（training support set）中采样 。在训练阶段，为了保证覆盖面，会为候选列表中的每种实体类型都提供至少一个示例 。}
}

@misc{zhang2024incontextlearningfewshotnested,
  title         = {In-Context Learning for Few-Shot Nested Named Entity Recognition},
  author        = {Meishan Zhang and Bin Wang and Hao Fei and Min Zhang},
  year          = {2024},
  eprint        = {2402.01182},
  archiveprefix = {arXiv},
  primaryclass  = {cs.CL},
  url           = {https://arxiv.org/abs/2402.01182},
  annotation    = {该论文提出了一个专为少样本嵌套NER设计的、基于ICL的先进框架。其核心贡献是创新的EnDe Retriever，
                   
                   EnDe Retriever的创新之处在于，它通过对比学习来优化检索过程，综合考量以下三个维度的相似性：
                   
                   
                   
                   语义相似性 (Semantic Similarity)：确保检索到的示例在句子语义上与测试样本相近 。
                   
                   边界相似性 (Boundary Similarity)：这是针对嵌套NER的关键创新。通过引入词性标注 (POS tags) 和句法成分树 (constituency trees) 等特征，让边界结构相似的句子在表示空间中更接近，从而为模型提供更好的实体边界线索 。
                   
                   标签相似性 (Label Identification)：通过对比学习，拉近具有相同实体标签的示例，同时推开那些文本有重叠但标签不同的实体示例，以减少歧义
                   
                   它通过一个结合了语义、边界和标签三个维度的对比学习机制，来挑选高质量的示例。实验证明，这种精细化的示例选择策略非常有效，使得模型在极少量标注数据的情况下}
}


@misc{zhao_dynamic_2024,
  title      = {Dynamic Demonstrations Controller for In-Context Learning},
  url        = {http://arxiv.org/abs/2310.00385},
  doi        = {10.48550/arXiv.2310.00385},
  abstract   = {In-context learning ({ICL}) is a new paradigm for natural language processing ({NLP}), where a large language model ({LLM}) observes a small number of demonstrations and a test instance as its input, and directly makes predictions without updating model parameters. Previous studies have revealed that {ICL} is sensitive to the selection and the ordering of demonstrations. However, there are few studies regarding the impact of the demonstration number on the {ICL} performance within a limited input length of {LLM}, because it is commonly believed that the number of demonstrations is positively correlated with model performance. In this paper, we found this conclusion does not always hold true. Through pilot experiments, we discover that increasing the number of demonstrations does not necessarily lead to improved performance. Building upon this insight, we propose a Dynamic Demonstrations Controller (D2Controller), which can improve the {ICL} performance by adjusting the number of demonstrations dynamically. The experimental results show that D2Controller yields a 4.6\% relative improvement on ten different sizes of {LLMs} across ten datasets. Besides, we also extend our method to previous {ICL} models and achieve competitive results.},
  number     = {{arXiv}:2310.00385},
  publisher  = {{arXiv}},
  author     = {Zhao, Fei and Pang, Taotian and Wu, Zhen and Ma, Zheng and Huang, Shujian and Dai, Xinyu},
  urldate    = {2025-06-20},
  year       = {2024-12-11},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2310.00385 [cs]},
  keywords   = {Computer Science - Artificial Intelligence, Computer Science - Computation and Language},
  file       = {PDF:files/568/Zhao 等 - 2024 - Dynamic Demonstrations Controller for In-Context Learning.pdf:application/pdf}
}



@misc{rae2022scalinglanguagemodelsmethods,
      title={Scaling Language Models: Methods, Analysis & Insights from Training Gopher}, 
      author={Jack W. Rae and Sebastian Borgeaud and Trevor Cai and Katie Millican and Jordan Hoffmann and Francis Song and John Aslanides and Sarah Henderson and Roman Ring and Susannah Young and Eliza Rutherford and Tom Hennigan and Jacob Menick and Albin Cassirer and Richard Powell and George van den Driessche and Lisa Anne Hendricks and Maribeth Rauh and Po-Sen Huang and Amelia Glaese and Johannes Welbl and Sumanth Dathathri and Saffron Huang and Jonathan Uesato and John Mellor and Irina Higgins and Antonia Creswell and Nat McAleese and Amy Wu and Erich Elsen and Siddhant Jayakumar and Elena Buchatskaya and David Budden and Esme Sutherland and Karen Simonyan and Michela Paganini and Laurent Sifre and Lena Martens and Xiang Lorraine Li and Adhiguna Kuncoro and Aida Nematzadeh and Elena Gribovskaya and Domenic Donato and Angeliki Lazaridou and Arthur Mensch and Jean-Baptiste Lespiau and Maria Tsimpoukelli and Nikolai Grigorev and Doug Fritz and Thibault Sottiaux and Mantas Pajarskas and Toby Pohlen and Zhitao Gong and Daniel Toyama and Cyprien de Masson d'Autume and Yujia Li and Tayfun Terzi and Vladimir Mikulik and Igor Babuschkin and Aidan Clark and Diego de Las Casas and Aurelia Guy and Chris Jones and James Bradbury and Matthew Johnson and Blake Hechtman and Laura Weidinger and Iason Gabriel and William Isaac and Ed Lockhart and Simon Osindero and Laura Rimell and Chris Dyer and Oriol Vinyals and Kareem Ayoub and Jeff Stanway and Lorrayne Bennett and Demis Hassabis and Koray Kavukcuoglu and Geoffrey Irving},
      year={2022},
      eprint={2112.11446},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2112.11446}, 
}

@misc{smith2022usingdeepspeedmegatrontrain,
      title={Using DeepSpeed and Megatron to Train Megatron-Turing NLG 530B, A Large-Scale Generative Language Model}, 
      author={Shaden Smith and Mostofa Patwary and Brandon Norick and Patrick LeGresley and Samyam Rajbhandari and Jared Casper and Zhun Liu and Shrimai Prabhumoye and George Zerveas and Vijay Korthikanti and Elton Zhang and Rewon Child and Reza Yazdani Aminabadi and Julie Bernauer and Xia Song and Mohammad Shoeybi and Yuxiong He and Michael Houston and Saurabh Tiwary and Bryan Catanzaro},
      year={2022},
      eprint={2201.11990},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2201.11990}, 
}

@misc{raffel2023exploringlimitstransferlearning,
      title={Exploring the Limits of Transfer Learning with a Unified Text-to-Text Transformer}, 
      author={Colin Raffel and Noam Shazeer and Adam Roberts and Katherine Lee and Sharan Narang and Michael Matena and Yanqi Zhou and Wei Li and Peter J. Liu},
      year={2023},
      eprint={1910.10683},
      archivePrefix={arXiv},
      primaryClass={cs.LG},
      url={https://arxiv.org/abs/1910.10683}, 
}

@misc{roberts2020knowledgepackparameterslanguage,
      title={How Much Knowledge Can You Pack Into the Parameters of a Language Model?}, 
      author={Adam Roberts and Colin Raffel and Noam Shazeer},
      year={2020},
      eprint={2002.08910},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2002.08910}, 
}

@misc{guu2020realmretrievalaugmentedlanguagemodel,
      title={REALM: Retrieval-Augmented Language Model Pre-Training}, 
      author={Kelvin Guu and Kenton Lee and Zora Tung and Panupong Pasupat and Ming-Wei Chang},
      year={2020},
      eprint={2002.08909},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2002.08909},
}

@misc{wang_gpt-ner_2023,
  title         = {GPT-NER: Named Entity Recognition via Large Language Models},
  author        = {Shuhe Wang and Xiaofei Sun and Xiaoya Li and Rongbin Ouyang and Fei Wu and Tianwei Zhang and Jiwei Li and Guoyin Wang},
  year          = {2023},
  eprint        = {2304.10428},
  archiveprefix = {arXiv},
  primaryclass  = {cs.CL},
  url           = {https://arxiv.org/abs/2304.10428}
}

@misc{adiga2024designinginformativemetricsfewshot,
      title={Designing Informative Metrics for Few-Shot Example Selection}, 
      author={Rishabh Adiga and Lakshminarayanan Subramanian and Varun Chandrasekaran},
      year={2024},
      eprint={2403.03861},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2403.03861}, 
}

@misc{bai2025labelguidedincontextlearningnamed,
      title={Label-Guided In-Context Learning for Named Entity Recognition}, 
      author={Fan Bai and Hamid Hassanzadeh and Ardavan Saeedi and Mark Dredze},
      year={2025},
      eprint={2505.23722},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2505.23722}, 
}

@misc{adiga2024designinginformativemetricsfewshot,
      title={Designing Informative Metrics for Few-Shot Example Selection}, 
      author={Rishabh Adiga and Lakshminarayanan Subramanian and Varun Chandrasekaran},
      year={2024},
      eprint={2403.03861},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2403.03861}, 
}

@misc{liu2021makesgoodincontextexamples,
      title={What Makes Good In-Context Examples for GPT-$3$?}, 
      author={Jiachang Liu and Dinghan Shen and Yizhe Zhang and Bill Dolan and Lawrence Carin and Weizhu Chen},
      year={2021},
      eprint={2101.06804},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2101.06804}, 
}
</file>

</files>
