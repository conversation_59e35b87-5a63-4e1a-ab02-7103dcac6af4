{"knowledge_base": {"main_file": "NER_Knowledge_Base.md", "index_file": "NER_Knowledge_Base_Index.md", "update_guide": "update_knowledge_base.md", "version": "1.0.0", "last_updated": "2024-12-28", "total_papers": 25}, "sections": {"大语言模型与上下文学习": {"line_start": 13, "subsections": ["基础理论", "ICL机制研究"]}, "少样本与零样本NER方法": {"line_start": 35, "subsections": ["基于LLM的NER框架", "零样本方法", "多智能体协作"]}, "提示工程与示例选择": {"line_start": 89, "subsections": ["示例选择策略", "动态示例控制", "特殊提示技术", "对比学习方法"]}, "元学习与多智能体方法": {"line_start": 149, "subsections": ["元学习框架"]}, "检索增强与指令微调": {"line_start": 175, "subsections": ["指令微调方法", "检索增强生成", "嵌套NER专门方法"]}, "综合性研究与工具": {"line_start": 227, "subsections": ["综述与调研", "实用工具", "统一框架"]}, "技术挑战与解决方案": {"line_start": 258, "subsections": ["主要挑战", "应用扩展"]}, "核心设计思想与选择策略": {"line_start": 353, "subsections": ["示例选择的哲学演进", "正负样本利用的思想转变", "任务建模的范式选择", "知识注入的策略选择", "上下文优化的理念", "标注指导的策略演进", "任务分解的设计理念", "质量控制的思想", "模型能力利用的理念"]}, "关键技术趋势": {"line_start": 509, "subsections": ["范式转换", "多阶段精细化处理", "元学习与知识蒸馏", "智能示例选择策略演进", "多智能体协作架构", "检索增强与上下文优化", "领域适应与泛化能力", "理论与实践结合"]}}, "key_methods": {"GPT-NER": {"authors": "<PERSON> et al.", "year": 2023, "section": "少样本与零样本NER方法", "key_innovation": "原文改写格式，@@##标记", "bibkey": "wang2023gptner"}, "PromptNER": {"authors": "Ashok & Lipton", "year": 2023, "section": "少样本与零样本NER方法", "key_innovation": "明确实体类型定义，Seq2Seq范式", "bibkey": "ashok2023promptner"}, "C-ICL": {"authors": "<PERSON> et al.", "year": 2024, "section": "提示工程与示例选择", "key_innovation": "对比学习，正负样本结合", "bibkey": "mo2024cicl"}, "GuideNER": {"authors": "<PERSON> et al.", "year": 2024, "section": "提示工程与示例选择", "key_innovation": "标注指南优于具体示例", "bibkey": "huang2024<PERSON><PERSON><PERSON>"}, "P-ICL": {"authors": "<PERSON> et al.", "year": 2024, "section": "提示工程与示例选择", "key_innovation": "点实体作为辅助信息", "bibkey": "jiang2024picl"}, "VicunaNER": {"authors": "<PERSON>", "year": 2023, "section": "少样本与零样本NER方法", "key_innovation": "两阶段框架：识别+再识别", "bibkey": "ji2023<PERSON><PERSON><PERSON>"}, "CMAS": {"authors": "<PERSON> et al.", "year": 2025, "section": "少样本与零样本NER方法", "key_innovation": "四智能体协作框架", "bibkey": "wang2025cooperative"}, "MetaIE": {"authors": "<PERSON> et al.", "year": 2024, "section": "元学习与多智能体方法", "key_innovation": "符号蒸馏构建元模型", "bibkey": "peng2024metaie"}, "2INER": {"authors": "<PERSON> et al.", "year": 2023, "section": "检索增强与指令微调", "key_innovation": "融合指令微调和上下文学习", "bibkey": "zhang2023iner"}, "RA-IT": {"authors": "<PERSON><PERSON> et al.", "year": 2024, "section": "检索增强与指令微调", "key_innovation": "检索增强指令微调", "bibkey": "xie2024<PERSON>rieval"}, "EnDe Retriever": {"authors": "<PERSON> et al.", "year": 2024, "section": "检索增强与指令微调", "key_innovation": "三维相似性对比学习", "bibkey": "zhang2024incontext"}, "IDS": {"authors": "<PERSON> et al.", "year": 2024, "section": "提示工程与示例选择", "key_innovation": "迭代式演示选择", "bibkey": "qin2024incontext"}, "D2Controller": {"authors": "<PERSON> et al.", "year": 2024, "section": "提示工程与示例选择", "key_innovation": "动态调整示例数量", "bibkey": "zhao2024dynamic"}, "UIE": {"authors": "<PERSON> et al.", "year": 2022, "section": "综合性研究与工具", "key_innovation": "统一文本到结构生成框架", "bibkey": "lu2022unified"}}, "technical_concepts": {"上下文学习": ["ICL", "In-Context Learning"], "元函数": ["Meta Function", "λ(instruction, demonstrations, text)"], "原文改写": ["Text Rewriting", "@@## marking"], "硬负样本": ["Hard Negative Samples"], "点实体": ["Point Entities"], "类型相关特征": ["TRF", "Type-Related Features"], "符号蒸馏": ["Symbolic Distillation"], "三维相似性": ["Three-dimensional Similarity"], "迭代示例选择": ["Iterative Demonstration Selection"], "检索增强指令微调": ["RA-IT", "Retrieval Augmented Instruction Tuning"]}, "update_tracking": {"next_bibkey_to_process": null, "pending_updates": [], "last_bib_file_hash": null, "auto_update_enabled": false}, "search_patterns": {"method_patterns": ["**{authors} ({year})** - {title}", "- **核心创新**：", "- **技术方法**：", "- **实验结果**："], "section_patterns": ["### {subsection_name}", "## {section_name}"]}}