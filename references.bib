
@misc{adiga_designing_2024,
  title      = {Designing Informative Metrics for Few-Shot Example Selection},
  url        = {http://arxiv.org/abs/2403.03861},
  doi        = {10.48550/arXiv.2403.03861},
  abstract   = {Pretrained language models ({PLMs}) have shown remarkable few-shot learning capabilities when provided with properly formatted examples. However, selecting the "best" examples remains an open challenge. We propose a complexity-based prompt selection approach for sequence tagging tasks. This approach avoids the training of a dedicated model for selection of examples, and instead uses certain metrics to align the syntactico-semantic complexity of test sentences and examples. We use both sentence- and word-level metrics to match the complexity of examples to the (test) sentence being considered. Our results demonstrate that our approach extracts greater performance from {PLMs}: it achieves state-of-the-art performance on few-shot {NER}, achieving a 5\% absolute improvement in F1 score on the {CoNLL}2003 dataset for {GPT}-4. We also see large gains of upto 28.85 points (F1/Acc.) in smaller models like {GPT}-j-6B.},
  number     = {{arXiv}:2403.03861},
  publisher  = {{arXiv}},
  author     = {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>},
  urldate    = {2025-06-20},
  year       = {2024-07-30},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2403.03861 [cs]},
  keywords   = {Computer Science - Computation and Language, Computer Science - Machine Learning},
  file       = {PDF:files/536/Adiga 等 - 2024 - Designing Informative Metrics for Few-Shot Example Selection.pdf:application/pdf}
}

@misc{ashok_promptner_2023,
  title      = {{PromptNER}: Prompting For Named Entity Recognition},
  url        = {http://arxiv.org/abs/2305.15444},
  doi        = {10.48550/arXiv.2305.15444},
  shorttitle = {{PromptNER}},
  abstract   = {In a surprising turn, Large Language Models ({LLMs}) together with a growing arsenal of prompt-based heuristics now offer powerful off-the-shelf approaches providing few-shot solutions to myriad classic {NLP} problems. However, despite promising early results, these {LLM}-based few-shot methods remain far from the state of the art in Named Entity Recognition ({NER}), where prevailing methods include learning representations via end-to-end structural understanding and fine-tuning on standard labeled corpora. In this paper, we introduce {PromptNER} , a new state-of-the-art algorithm for few-Shot and cross-domain {NER}. To adapt to any new {NER} task {PromptNER} requires a set of entity definitions in addition to the standard few-shot examples. Given a sentence, {PromptNER} prompts an {LLM} to produce a list of potential entities along with corresponding explanations justifying their compatibility with the provided entity type definitions. {PromptNER} achieves state-of-the-art performance on fewshot {NER}, achieving a 4\% (absolute) improvement in F1 score on the {ConLL} dataset, a 9\% (absolute) improvement on the {GENIA} dataset, and a 4\% (absolute) improvement on the {FewNERD} dataset. {PromptNER} also moves the state of the art on Cross Domain {NER}, outperforming prior methods (including those not limited to the few-shot setting), setting a new mark on 3/5 {CrossNER} target domains, with an average F1 gain of 3\%, despite using less than 2\% of the available data.},
  number     = {{arXiv}:2305.15444},
  publisher  = {{arXiv}},
  author     = {Ashok, Dhananjay and Lipton, Zachary C.},
  urldate    = {2025-06-20},
  year       = {2023-06-20},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2305.15444 [cs]},
  keywords   = {Computer Science - Artificial Intelligence, Computer Science - Computation and Language, Computer Science - Machine Learning},
  annotation = {
                
                该论文认为，传统的少样本（Few-Shot）{ICL方法在NER上表现不佳}，是因为仅提供示例不足以让{LLM理解特定领域中}“什么才算是一个实体”。因此，他们提出 {PromptNER}，其核心是在Prompt中除了提供少量示例外，还必须提供一个明确的、模块化的实体类型定义（entity definitions） 。
                
                
                
                主要方法:
                
                
                模块化定义 (Modular Definitions): 为每个{NER任务创建一个自然语言的定义文档}，详细描述每个实体类型应该包含什么、不应该包含什么 。这个定义可以灵活替换，以适应不同领域 。
                
                
                链式思考式输出 (Chain-of-Thought like Output): {Prompt不仅要求LLM识别实体}，还要求它对每个候选实体做出判断（是/否为实体），并提供一句话的**解释（explanation）**来证明其判断的合理性 。这种结构化的输出格式，促使模型进行更深入的思考。
                
                
                Seq2Seq范式: 将{NER任务建模为序列到序列的生成任务}，而不是传统的序列标注任务，这样能更好地处理多词实体，并与{CoT等推理方法结合} 。
                
                
                
                
                
                
                
                },
  file       = {PDF:files/531/Ashok和Lipton - 2023 - PromptNER Prompting For Named Entity Recognition.pdf:application/pdf}
}

@inproceedings{babych_improving_2003,
  title      = {Improving Machine Translation Quality with Automatic Named Entity Recognition},
  url        = {https://aclanthology.org/W03-2201/},
  eventtitle = {{EAMT} 2003},
  booktitle  = {Proceedings of the 7th International {EAMT} workshop on {MT} and other language technology tools, Improving {MT} through other language technology tools, Resource and tools for building {MT} at {EACL} 2003},
  author     = {Babych, Bogdan and Hartley, Anthony},
  urldate    = {2025-06-21},
  year       = {2003},
  annotation = {机器翻译
                
                },
  file       = {Full Text PDF:files/643/Babych和Hartley - 2003 - Improving Machine Translation Quality with Automatic Named Entity Recognition.pdf:application/pdf}
}

@misc{brown2020languagemodelsfewshotlearners,
  title         = {Language Models are Few-Shot Learners},
  author        = {Tom B. Brown and Benjamin Mann and Nick Ryder and Melanie Subbiah and Jared Kaplan and Prafulla Dhariwal and Arvind Neelakantan and Pranav Shyam and Girish Sastry and Amanda Askell and Sandhini Agarwal and Ariel Herbert-Voss and Gretchen Krueger and Tom Henighan and Rewon Child and Aditya Ramesh and Daniel M. Ziegler and Jeffrey Wu and Clemens Winter and Christopher Hesse and Mark Chen and Eric Sigler and Mateusz Litwin and Scott Gray and Benjamin Chess and Jack Clark and Christopher Berner and Sam McCandlish and Alec Radford and Ilya Sutskever and Dario Amodei},
  year          = {2020},
  eprint        = {2005.14165},
  archiveprefix = {arXiv},
  primaryclass  = {cs.CL},
  url           = {https://arxiv.org/abs/2005.14165}
}

@misc{chen_learning_2023,
  title      = {Learning In-context Learning for Named Entity Recognition},
  url        = {http://arxiv.org/abs/2305.11038},
  doi        = {10.48550/arXiv.2305.11038},
  abstract   = {Named entity recognition in real-world applications suffers from the diversity of entity types, the emergence of new entity types, and the lack of high-quality annotations. To address the above problems, this paper proposes an in-context learning-based {NER} approach, which can effectively inject in-context {NER} ability into {PLMs} and recognize entities of novel types on-the-fly using only a few demonstrative instances. Specifically, we model {PLMs} as a meta-function λinstruction, demonstrations, text.M1, and a new entity extractor can be implicitly constructed by applying new instruction and demonstrations to {PLMs}, i.e., (λ.M)(instruction, demonstrations) → F where F will be a new entity extractor, i.e., F: text → entities. To inject the above in-context {NER} ability into {PLMs}, we propose a meta-function pre-training algorithm, which pre-trains {PLMs} by comparing the (instruction, demonstration)-initialized extractor with a surrogate golden extractor. Experimental results on 4 few-shot {NER} datasets show that our method can effectively inject in-context {NER} ability into {PLMs} and significantly outperforms the {PLMs}+fine-tuning counterparts.},
  number     = {{arXiv}:2305.11038},
  publisher  = {{arXiv}},
  author     = {Chen, Jiawei and Lu, Yaojie and Lin, Hongyu and Lou, Jie and Jia, Wei and Dai, Dai and Wu, Hua and Cao, Boxi and Han, Xianpei and Sun, Le},
  urldate    = {2025-06-20},
  year       = {2023-05-26},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2305.11038 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {Comment: Accepted to {ACL} 2023 Main Conference},
  annotation = {Comment: Accepted to {ACL} 2023 Main Conference},
  file       = {PDF:files/535/Chen 等 - 2023 - Learning In-context Learning for Named Entity Recognition.pdf:application/pdf}
}

@inproceedings{chen_self-icl_2023,
  location   = {Singapore},
  title      = {Self-{ICL}: Zero-Shot In-Context Learning with Self-Generated Demonstrations},
  url        = {https://aclanthology.org/2023.emnlp-main.968},
  doi        = {10.18653/v1/2023.emnlp-main.968},
  shorttitle = {Self-{ICL}},
  eventtitle = {Proceedings of the 2023 Conference on Empirical Methods in Natural Language Processing},
  pages      = {15651--15662},
  booktitle  = {Proceedings of the 2023 Conference on Empirical Methods in Natural Language Processing},
  publisher  = {Association for Computational Linguistics},
  author     = {Chen, Wei-Lin and Wu, Cheng-Kuang and Chen, Yun-Nung and Chen, Hsin-Hsi},
  urldate    = {2025-06-20},
  year       = {2023},
  langid     = {english},
  file       = {PDF:files/538/Chen 等 - 2023 - Self-ICL Zero-Shot In-Context Learning with Self-Generated Demonstrations.pdf:application/pdf}
}

@article{huang_guidener_nodate,
  title      = {{GuideNER}: Annotation Guidelines Are Better than Examples for In-Context Named Entity Recognition},
  abstract   = {Large language models ({LLMs}) demonstrate impressive performance on downstream tasks through in-context learning ({ICL}). However, there is a significant gap between their performance in Named Entity Recognition ({NER}) and in finetuning methods. We believe this discrepancy is due to inconsistencies in labeling definitions in {NER}. In addition, recent research indicates that {LLMs} do not learn the specific inputlabel mappings from the demonstrations. Therefore, we argue that using examples to implicitly capture the mapping between inputs and labels in in-context learning is not suitable for {NER}. Instead, it requires explicitly informing the model of the range of entities contained in the labels, such as annotation guidelines. In this paper, we propose {GuideNER}, which uses {LLMs} to summarize concise annotation guidelines as contextual information in {ICL}. We have conducted experiments on widely used {NER} datasets, and the experimental results indicate that our method can consistently and significantly outperform state-of-the-art methods, while using shorter prompts. Especially on the {GENIA} dataset, our model outperforms the previous state-of-the-art model by 12.63 F1 scores.},
  author     = {Huang, Shizhou and Xu, Bo and Yu, Yang and Li, Changqun and Lin, Xin Alex},
  langid     = {english},
  annotation = {
                
                对于{NER任务}，在上下文学习（In-Context Learning, {ICL}）中，直接给模型看几个输入输出的例子（examples），效果并不好。因为{LLM不一定能从有限的}、具体的例子中正确归纳出抽象的标注规则 。更有效的方法是，直接告诉模型明确的“标注指南”（Annotation Guidelines） 。
                
                
                
                主要方法 (三步法):
                
                
                总结模式 (Summarize Patterns): 使用{LLM从训练集的每个}（输入-标签）对中，总结出通用的模式或规则 。
                
                
                自我验证 (Self-Verify): 再次使用{LLM}，验证上一步总结出的规则是否能够准确地预测回原始的标签。过滤掉那些导致错误预测的、有歧义的规则 。
                
                
                聚合规则 (Aggregate Rules): 统计所有经过验证的准确规则的出现频率，筛选出最高频、最通用的Top-K条规则，聚合成最终给用户的“标注指南” 。
                
                
                
                
                关键发现:
                
                
                使用{LLM生成的简洁}“标注指南”作为上下文，性能显著优于使用一堆“示例”作为上下文的传统{ICL方法} 。
                
                
                该方法在多个数据集上达到{SOTA}（State-of-the-art）性能，尤其在{GENIA数据集上提升了}12.63{个F}1点，且Prompt长度更短 。
                
                
                
                
                },
  file       = {PDF:files/539/Huang 等 - GuideNER Annotation Guidelines Are Better than Examples for In-Context Named Entity Recognition.pdf:application/pdf}
}

@misc{ji_vicunaner_2023,
  title      = {{VicunaNER}: Zero/Few-shot Named Entity Recognition using Vicuna},
  url        = {http://arxiv.org/abs/2305.03253},
  doi        = {10.48550/arXiv.2305.03253},
  shorttitle = {{VicunaNER}},
  abstract   = {Large Language Models ({LLMs}, e.g., {ChatGPT}) have shown impressive zero- and fewshot capabilities in Named Entity Recognition ({NER}). However, these models can only be accessed via online {APIs}, which may cause data leak and non-reproducible problems. In this paper, we propose {VicunaNER}, a zero/fewshot {NER} framework based on the newly released open-source {LLM} – Vicuna. {VicunaNER} is a two-phase framework, where each phase leverages multi-turn dialogues with Vicuna to recognize entities from texts. We name the second phase as Re-Recognition, which recognizes those entities not recognized in the ﬁrst phase (a.k.a. Recongition). Moreover, we set entity correctness check dialogues in each phase to ﬁlter out wrong entities. We evaluate {VicunaNER}’s zero-shot capacity on 10 datasets crossing 5 domains and few-shot capacity on Few-{NERD}. Experimental results demonstrate that {VicunaNER} achieves superior performance in both shot settings. Additionally, we conduct comprehensive investigations on Vicuna from multiple perspectives.},
  number     = {{arXiv}:2305.03253},
  publisher  = {{arXiv}},
  author     = {Ji, Bin},
  urldate    = {2025-06-20},
  year       = {2023-05-05},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2305.03253 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                该论文基于开源模型Vicuna，提出了一个两阶段的{NER框架}，旨在解决单次识别（Recognition）中实体召回率不足的问题。
                
                
                主要方法:
                
                
                两阶段识别 (Two-phase Framework):
                
                
                阶段一：识别 (Recognition): 使用多轮对话进行初次实体识别，并包含一个“正确性检查”对话环节，用于过滤掉明显错误的实体 。
                
                
                阶段二：再识别 (Re-Recognition): 将第一阶段识别出的实体作为已知信息，再次Prompt模型，要求它专门去识别那些“在第一阶段被遗漏的”实体 。
                
                
                
                
                实体合并: 将两个阶段识别出的实体列表进行合并去重，得到最终结果 。
                
                
                
                
                },
  file       = {PDF:files/529/Ji - 2023 - VicunaNER ZeroFew-shot Named Entity Recognition using Vicuna.pdf:application/pdf}
}

@misc{jiang_p-icl_2024,
  title      = {P-{ICL}: Point In-Context Learning for Named Entity Recognition with Large Language Models},
  url        = {http://arxiv.org/abs/2405.04960},
  doi        = {10.48550/arXiv.2405.04960},
  shorttitle = {P-{ICL}},
  abstract   = {In recent years, the rise of large language models ({LLMs}) has made it possible to directly achieve named entity recognition ({NER}) without any demonstration samples or only using a few samples through in-context learning ({ICL}). However, standard {ICL} only helps {LLMs} understand task instructions, format and input-label mapping, but neglects the particularity of the {NER} task itself. In this paper, we propose a new prompting framework P-{ICL} to better achieve {NER} with {LLMs}, in which some point entities are leveraged as the auxiliary information to recognize each entity type. With such significant information, the {LLM} can achieve entity classification more precisely. To obtain optimal point entities for prompting {LLMs}, we also proposed a point entity selection method based on K-Means clustering. Our extensive experiments on some representative {NER} benchmarks verify the effectiveness of our proposed strategies in P-{ICL} and point entity selection.},
  number     = {{arXiv}:2405.04960},
  publisher  = {{arXiv}},
  author     = {Jiang, Guochao and Ding, Zepeng and Shi, Yuchen and Yang, Deqing},
  urldate    = {2025-06-20},
  year       = {2024-06-17},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2405.04960 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                标准{ICL提供的完整句子示例信息冗余}，且忽略了{NER任务的特殊性} 。该论文认为，直接为每个实体类型提供一组**代表性的实体词（Point Entities）**作为示例，比提供完整的句子更有效。
                
                
                
                主要方法:
                
                
                P-{ICL框架}: 在Prompt中，除了任务指令和少量few-shot句子示例外，为每个实体类型（如{PER}, {LOC}, {ORG}）明确列出一些典型的实体词（例如，{LOC}: London, China, California）。
                
                
                Point Entity选择: 为了选出有代表性的实体词，作者使用了K-Means聚类方法。他们先将训练集中所有同类实体用{BERT等模型编码为向量}，然后进行K-Means聚类，最后选取离每个簇中心最近的实体词作为“Point Entities” 。
                
                
                
                
                
                
                
                },
  file       = {PDF:files/563/Jiang 等 - 2024 - P-ICL Point In-Context Learning for Named Entity Recognition with Large Language Models.pdf:application/pdf}
}

@misc{keraghel_recent_2024,
  title      = {Recent Advances in Named Entity Recognition: A Comprehensive Survey and Comparative Study},
  url        = {http://arxiv.org/abs/2401.10825},
  doi        = {10.48550/arXiv.2401.10825},
  shorttitle = {Recent Advances in Named Entity Recognition},
  abstract   = {Named Entity Recognition seeks to extract substrings within a text that name real-world objects and to determine their type (for example, whether they refer to persons or organizations). In this survey, we first present an overview of recent popular approaches, including advancements in Transformer-based methods and Large Language Models ({LLMs}) that have not had much coverage in other surveys. In addition, we discuss reinforcement learning and graph-based approaches, highlighting their role in enhancing {NER} performance. Second, we focus on methods designed for datasets with scarce annotations. Third, we evaluate the performance of the main {NER} implementations on a variety of datasets with differing characteristics (as regards their domain, their size, and their number of classes). We thus provide a deep comparison of algorithms that have never been considered together. Our experiments shed some light on how the characteristics of datasets affect the behavior of the methods we compare.},
  number     = {{arXiv}:2401.10825},
  publisher  = {{arXiv}},
  author     = {Keraghel, Imed and Morbieu, Stanislas and Nadif, Mohamed},
  urldate    = {2025-06-20},
  year       = {2024-12-20},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2401.10825 [cs]},
  keywords   = {Computer Science - Computation and Language, Computer Science - Machine Learning},
  annotation = {Comment: 42 pages},
  file       = {PDF:files/572/Keraghel 等 - 2024 - Recent Advances in Named Entity Recognition A Comprehensive Survey and Comparative Study.pdf:application/pdf}
}

@misc{keraghel_recent_2024-1,
  title      = {Recent Advances in Named Entity Recognition: A Comprehensive Survey and Comparative Study},
  url        = {http://arxiv.org/abs/2401.10825},
  doi        = {10.48550/arXiv.2401.10825},
  shorttitle = {Recent Advances in Named Entity Recognition},
  abstract   = {Named Entity Recognition seeks to extract substrings within a text that name real-world objects and to determine their type (for example, whether they refer to persons or organizations). In this survey, we first present an overview of recent popular approaches, including advancements in Transformer-based methods and Large Language Models ({LLMs}) that have not had much coverage in other surveys. In addition, we discuss reinforcement learning and graph-based approaches, highlighting their role in enhancing {NER} performance. Second, we focus on methods designed for datasets with scarce annotations. Third, we evaluate the performance of the main {NER} implementations on a variety of datasets with differing characteristics (as regards their domain, their size, and their number of classes). We thus provide a deep comparison of algorithms that have never been considered together. Our experiments shed some light on how the characteristics of datasets affect the behavior of the methods we compare.},
  number     = {{arXiv}:2401.10825},
  publisher  = {{arXiv}},
  author     = {Keraghel, Imed and Morbieu, Stanislas and Nadif, Mohamed},
  urldate    = {2025-06-21},
  year       = {2024-12-20},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2401.10825 [cs]},
  keywords   = {Computer Science - Computation and Language, Computer Science - Machine Learning},
  annotation = {Comment: 42 pages},
  file       = {PDF:files/639/Keraghel 等 - 2024 - Recent Advances in Named Entity Recognition A Comprehensive Survey and Comparative Study.pdf:application/pdf}
}

@misc{lee_good_2022,
  title      = {Good Examples Make A Faster Learner: Simple Demonstration-based Learning for Low-resource {NER}},
  url        = {http://arxiv.org/abs/2110.08454},
  doi        = {10.48550/arXiv.2110.08454},
  shorttitle = {Good Examples Make A Faster Learner},
  abstract   = {Recent advances in prompt-based learning have shown strong results on few-shot text classification by using cloze-style templates. Similar attempts have been made on named entity recognition ({NER}) which manually design templates to predict entity types for every text span in a sentence. However, such methods may suffer from error propagation induced by entity span detection, high cost due to enumeration of all possible text spans, and omission of inter-dependencies among token labels in a sentence. Here we present a simple demonstration-based learning method for {NER}, which lets the input be prefaced by task demonstrations for in-context learning. We perform a systematic study on demonstration strategy regarding what to include (entity examples, with or without surrounding context), how to select the examples, and what templates to use. Results on in-domain learning and domain adaptation show that the model's performance in low-resource settings can be largely improved with a suitable demonstration strategy (e.g., a 4-17\% improvement on 25 train instances). We also find that good demonstration can save many labeled examples and consistency in demonstration contributes to better performance.},
  number     = {{arXiv}:2110.08454},
  publisher  = {{arXiv}},
  author     = {Lee, Dong-Ho and Kadakia, Akshen and Tan, Kangmin and Agarwal, Mahak and Feng, Xinyu and Shibuya, Takashi and Mitani, Ryosuke and Sekiya, Toshiyuki and Pujara, Jay and Ren, Xiang},
  urldate    = {2025-06-20},
  year       = {2022-03-31},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2110.08454 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {Comment: Accepted to {ACL} 2022 main conference. 14 pages, 8 figures, 9 tables},
  file       = {PDF:files/567/Lee 等 - 2022 - Good Examples Make A Faster Learner Simple Demonstration-based Learning for Low-resource NER.pdf:application/pdf}
}

@misc{li_rethinking_2022,
  title      = {Rethinking Negative Sampling for Handling Missing Entity Annotations},
  url        = {http://arxiv.org/abs/2108.11607},
  doi        = {10.48550/arXiv.2108.11607},
  abstract   = {Negative sampling is highly effective in handling missing annotations for named entity recognition ({NER}). One of our contributions is an analysis on how it makes sense through introducing two insightful concepts: missampling and uncertainty. Empirical studies show low missampling rate and high uncertainty are both essential for achieving promising performances with negative sampling. Based on the sparsity of named entities, we also theoretically derive a lower bound for the probability of zero missampling rate, which is only relevant to sentence length. The other contribution is an adaptive and weighted sampling distribution that further improves negative sampling via our former analysis. Experiments on synthetic datasets and well-annotated datasets (e.g., {CoNLL}-2003) show that our proposed approach beneﬁts negative sampling in terms of F1 score and loss convergence. Besides, models with improved negative sampling have achieved new state-of-the-art results on realworld datasets (e.g., {EC}).},
  number     = {{arXiv}:2108.11607},
  publisher  = {{arXiv}},
  author     = {Li, Yangming and Liu, Lemao and Shi, Shuming},
  urldate    = {2025-06-20},
  year       = {2022-02-25},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2108.11607 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                这篇论文主要研究在训练数据标注不完整（存在大量“漏标”实体）的情况下，如何有效训练{NER模型}。它深入分析了“负采样”（Negative Sampling）技术为何有效 。
                
                
                
                主要方法与概念:
                
                
                负采样: 在训练时，不把所有未标注的文本片段都当作负样本，而是从中随机采样一小部分作为负样本。这可以有效避免模型被“漏标”的实体（本应是正样本，却被当作负样本）误导 。
                
                
                错采样率 (Missampling Rate): 定义了在采样的负样本中，不慎包含了“漏标”实体的比例。论文证明，低错采样率是性能的关键 。
                
                
                不确定性 (Uncertainty): 指出采样那些模型“最不确定”的、最难判断的负样本进行训练，比采样简单的负样本效果更好 。
                
                
                加权采样: 基于以上分析，提出了一种加权的、自适应的采样方法，优先采样那些“不确定性高”且“错采样率低”的负样本 。
                
                
                
                
                
                
                
                },
  annotation = {Comment: A long paper accepted by {ACL}-2022},
  annotation = {Comment: A long paper accepted by {ACL}-2022},
  file       = {PDF:files/534/Li 等 - 2022 - Rethinking Negative Sampling for Handling Missing Entity Annotations.pdf:application/pdf}
}

@misc{lu_unified_2022,
  title      = {Unified Structure Generation for Universal Information Extraction},
  url        = {http://arxiv.org/abs/2203.12277},
  doi        = {10.48550/arXiv.2203.12277},
  abstract   = {Information extraction suffers from its varying targets, heterogeneous structures, and demandspeciﬁc schemas. In this paper, we propose a uniﬁed text-to-structure generation framework, namely {UIE}, which can universally model different {IE} tasks, adaptively generate targeted structures, and collaboratively learn general {IE} abilities from different knowledge sources. Speciﬁcally, {UIE} uniformly encodes different extraction structures via a structured extraction language, adaptively generates target extractions via a schema-based prompt mechanism – structural schema instructor, and captures the common {IE} abilities via a large-scale pretrained text-to-structure model. Experiments show that {UIE} achieved the state-of-the-art performance on 4 {IE} tasks, 13 datasets, and on all supervised, low-resource, and few-shot settings for a wide range of entity, relation, event and sentiment extraction tasks and their uniﬁcation. These results veriﬁed the effectiveness, universality, and transferability of {UIE}1.},
  number     = {{arXiv}:2203.12277},
  publisher  = {{arXiv}},
  author     = {Lu, Yaojie and Liu, Qing and Dai, Dai and Xiao, Xinyan and Lin, Hongyu and Han, Xianpei and Sun, Le and Wu, Hua},
  urldate    = {2025-06-20},
  year       = {2022-03-23},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2203.12277 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {Comment: Accepted to the main conference of {ACL}2022},
  annotation = {Comment: Accepted to the main conference of {ACL}2022},
  annotation = {Comment: Accepted to the main conference of {ACL}2022},
  annotation = {Comment: Accepted to the main conference of {ACL}2022},
  file       = {PDF:files/528/Lu 等 - 2022 - Unified Structure Generation for Universal Information Extraction.pdf:application/pdf}
}

@misc{meng_query-based_2019,
  title      = {Query-Based Named Entity Recognition},
  url        = {http://arxiv.org/abs/1908.09138},
  doi        = {10.48550/arXiv.1908.09138},
  abstract   = {In this paper, we propose a new strategy for the task of named entity recognition ({NER}). We cast the task as a query-based machine reading comprehension task: e.g., the task of extracting entities with {PER} is formalized as answering the question of "which person is mentioned in the text ?". Such a strategy comes with the advantage that it solves the long-standing issue of handling overlapping or nested entities (the same token that participates in more than one entity categories) with sequence-labeling techniques for {NER}. Additionally, since the query encodes informative prior knowledge, this strategy facilitates the process of entity extraction, leading to better performances. We experiment the proposed model on five widely used {NER} datasets on English and Chinese, including {MSRA}, Resume, {OntoNotes}, {ACE}04 and {ACE}05. The proposed model sets new {SOTA} results on all of these datasets.},
  number     = {{arXiv}:1908.09138},
  publisher  = {{arXiv}},
  author     = {Meng, Yuxian and Li, Xiaoya and Sun, Zijun and Li, Jiwei},
  urldate    = {2025-06-21},
  year       = {2019-11-02},
  eprinttype = {arxiv},
  eprint     = {1908.09138 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {信息检索
                
                },
  annotation = {Comment: Please refer to the full version of this paper: A unified framework for named entity recognition {arXiv}:1910.11476},
  file       = {Preprint PDF:files/650/Meng 等 - 2019 - Query-Based Named Entity Recognition.pdf:application/pdf;Snapshot:files/651/1908.html:text/html}
}

@misc{min_rethinking_2022,
  title      = {Rethinking the Role of Demonstrations: What Makes In-Context Learning Work?},
  url        = {http://arxiv.org/abs/2202.12837},
  doi        = {10.48550/arXiv.2202.12837},
  shorttitle = {Rethinking the Role of Demonstrations},
  abstract   = {Large language models ({LMs}) are able to incontext learn—perform a new task via inference alone by conditioning on a few inputlabel pairs (demonstrations) and making predictions for new inputs. However, there has been little understanding of how the model learns and which aspects of the demonstrations contribute to end task performance. In this paper, we show that ground truth demonstrations are in fact not required—randomly replacing labels in the demonstrations barely hurts performance on a range of classiﬁcation and multi-choce tasks, consistently over 12 different models including {GPT}-3. Instead, we ﬁnd that other aspects of the demonstrations are the key drivers of end task performance, including the fact that they provide a few examples of (1) the label space, (2) the distribution of the input text, and (3) the overall format of the sequence. Together, our analysis provides a new way of understanding how and why in-context learning works, while opening up new questions about how much can be learned from large language models through inference alone.},
  number     = {{arXiv}:2202.12837},
  publisher  = {{arXiv}},
  author     = {Min, Sewon and Lyu, Xinxi and Holtzman, Ari and Artetxe, Mikel and Lewis, Mike and Hajishirzi, Hannaneh and Zettlemoyer, Luke},
  urldate    = {2025-06-20},
  year       = {2022-10-20},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2202.12837 [cs]},
  keywords   = {Computer Science - Artificial Intelligence, Computer Science - Computation and Language},
  annotation = {Comment: 17 pages; 12 figures. Published as a conference paper at {EMNLP} 2022 (long). Code available at https://github.com/Alrope123/rethinking-demonstrations},
  file       = {PDF:files/566/Min 等 - 2022 - Rethinking the Role of Demonstrations What Makes In-Context Learning Work.pdf:application/pdf}
}

@misc{mo_c-icl_2024,
  title      = {C-{ICL}: Contrastive In-context Learning for Information Extraction},
  url        = {http://arxiv.org/abs/2402.11254},
  doi        = {10.48550/arXiv.2402.11254},
  shorttitle = {C-{ICL}},
  abstract   = {There has been increasing interest in exploring the capabilities of advanced large language models ({LLMs}) in the field of information extraction ({IE}), specifically focusing on tasks related to named entity recognition ({NER}) and relation extraction ({RE}). Although researchers are exploring the use of few-shot information extraction through in-context learning with {LLMs}, they tend to focus only on using correct or positive examples for demonstration, neglecting the potential value of incorporating incorrect or negative examples into the learning process. In this paper, we present C-{ICL}, a novel few-shot technique that leverages both correct and incorrect sample constructions to create in-context learning demonstrations. This approach enhances the ability of {LLMs} to extract entities and relations by utilizing prompts that incorporate not only the positive samples but also the reasoning behind them. This method allows for the identification and correction of potential interface errors. Specifically, our proposed method taps into the inherent contextual information and valuable information in hard negative samples and the nearest positive neighbors to the test and then applies the in-context learning demonstrations based on {LLMs}. Our experiments on various datasets indicate that C-{ICL} outperforms previous few-shot in-context learning methods, delivering substantial enhancements in performance across a broad spectrum of related tasks. These improvements are noteworthy, showcasing the versatility of our approach in miscellaneous scenarios.},
  number     = {{arXiv}:2402.11254},
  publisher  = {{arXiv}},
  author     = {Mo, Ying and Liu, Jiahao and Yang, Jian and Wang, Qifan and Zhang, Shun and Wang, Jingang and Li, Zhoujun},
  urldate    = {2025-06-20},
  year       = {2024-06-24},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2402.11254 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {Comment: 15 pages},
  annotation = {Comment: 15 pages},
  file       = {PDF:files/540/Mo 等 - 2024 - C-ICL Contrastive In-context Learning for Information Extraction.pdf:application/pdf}
}

@article{molla_named_nodate,
  title      = {Named Entity Recognition for Question Answering},
  abstract   = {Current text-based question answering ({QA}) systems usually contain a named entity recogniser ({NER}) as a core component. Named entity recognition has traditionally been developed as a component for information extraction systems, and current techniques are focused on this end use. However, no formal assessment has been done on the characteristics of a {NER} within the task of question answering. In this paper we present a {NER} that aims at higher recall by allowing multiple entity labels to strings. The {NER} is embedded in a question answering system and the overall {QA} system performance is compared to that of one with a traditional variation of the {NER} that only allows single entity labels. It is shown that the added noise produced introduced by the additional labels is offset by the higher recall gained, therefore enabling the {QA} system to have a better chance to ﬁnd the answer.},
  author     = {Molla, Diego and van Zaanen, Menno and Smith, Daniel},
  langid     = {english},
  annotation = {qa
                },
  year       = {2006},
  file       = {PDF:files/645/Molla 等 - Named Entity Recognition for Question Answering.pdf:application/pdf}
}

@online{noauthor_231009881v4_nodate,
  title   = {[2310.09881v4] In-Context Learning with Iterative Demonstration Selection},
  url     = {https://arxiv.org/abs/2310.09881v4},
  urldate = {2025-06-20},
  file    = {[2310.09881v4] In-Context Learning with Iterative Demonstration Selection:files/521/2310.html:text/html}
}

@misc{peng_metaie_2024,
  title      = {{MetaIE}: Distilling a Meta Model from {LLM} for All Kinds of Information Extraction Tasks},
  url        = {http://arxiv.org/abs/2404.00457},
  doi        = {10.48550/arXiv.2404.00457},
  shorttitle = {{MetaIE}},
  abstract   = {Information extraction ({IE}) is a fundamental area in natural language processing where prompting large language models ({LLMs}), even with in-context examples, cannot defeat small {LMs} tuned on very small {IE} datasets. We observe that {IE} tasks, such as named entity recognition and relation extraction, all focus on extracting important information, which can be formalized as a label-to-span matching. In this paper, we propose a novel framework {MetaIE} to build a small {LM} as meta-model by learning to extract "important information", i.e., the meta-understanding of {IE}, so that this meta-model can be adapted to all kind of {IE} tasks effectively and efficiently. Specifically, {MetaIE} obtains the small {LM} via a symbolic distillation from an {LLM} following the label-to-span scheme. We construct the distillation dataset via sampling sentences from language model pre-training datasets (e.g., {OpenWebText} in our implementation) and prompting an {LLM} to identify the typed spans of "important information". We evaluate the meta-model under the few-shot adaptation setting. Extensive results on 13 datasets from 6 {IE} tasks confirm that {MetaIE} can offer a better starting point for few-shot tuning on {IE} datasets and outperform other meta-models from (1) vanilla language model pre-training, (2) multi-{IE}-task pre-training with human annotations, and (3) single-{IE}-task symbolic distillation from {LLM}. Moreover, we provide comprehensive analyses of {MetaIE}, such as the size of the distillation dataset, the meta-model architecture, and the size of the meta-model.},
  number     = {{arXiv}:2404.00457},
  publisher  = {{arXiv}},
  author     = {Peng, Letian and Wang, Zilong and Yao, Feng and Wang, Zihan and Shang, Jingbo},
  urldate    = {2025-06-20},
  year       = {2024-03-30},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2404.00457 [cs]},
  keywords   = {Computer Science - Computation and Language},
  file       = {PDF:files/562/Peng 等 - 2024 - MetaIE Distilling a Meta Model from LLM for All Kinds of Information Extraction Tasks.pdf:application/pdf}
}

@misc{qin_-context_2024,
  title      = {In-Context Learning with Iterative Demonstration Selection},
  url        = {http://arxiv.org/abs/2310.09881},
  doi        = {10.48550/arXiv.2310.09881},
  abstract   = {Spurred by advancements in scale, large language models ({LLMs}) have demonstrated strong few-shot learning ability via in-context learning ({ICL}). However, the performance of {ICL} has been shown to be highly sensitive to the selection of few-shot demonstrations. Selecting the most suitable examples as context remains an ongoing challenge and an open problem. Existing literature has highlighted the importance of selecting examples that are diverse or semantically similar to the test sample while ignoring the fact that the optimal selection dimension, i.e., diversity or similarity, is task-specific. Based on how the test sample is answered, we propose Iterative Demonstration Selection ({IDS}) to leverage the merits of both dimensions. Using zero-shot chain-of-thought reasoning (Zero-shot-{CoT}), {IDS} iteratively selects examples that are diverse but still strongly correlated with the test sample as {ICL} demonstrations. Specifically, {IDS} applies Zero-{shotCoT} to the test sample before demonstration selection. The output reasoning path is then used to choose demonstrations that are prepended to the test sample for inference. The generated answer is followed by its corresponding reasoning path for extracting a new set of demonstrations in the next iteration. After several iterations, {IDS} adopts majority voting to obtain the final result. Through extensive experiments on tasks including reasoning, question answering, and topic classification, we demonstrate that {IDS} can consistently outperform existing {ICL} demonstration selection methods.},
  number     = {{arXiv}:2310.09881},
  publisher  = {{arXiv}},
  author     = {Qin, Chengwei and Zhang, Aston and Chen, Chen and Dagar, Anirudh and Ye, Wenming},
  urldate    = {2025-06-20},
  year       = {2024-12-30},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2310.09881 [cs]},
  keywords   = {Computer Science - Artificial Intelligence, Computer Science - Computation and Language},
  annotation = {
                
                该论文指出，{ICL演示}（demonstrations）的最佳选择标准（是追求“多样性”还是“相似性”）是依任务而定的 。为此，它提出了一种**迭代式演示选择（{IDS}）**方法，试图结合两者的优点。
                
                
                
                主要方法:
                
                
                从Zero-shot-{CoT开始}: 对于一个测试样本，不先选演示，而是先用Zero-shot-{CoT}（例如，在末尾加上“Let's think step by step.”）让{LLM生成一个初步的推理路径} 。
                
                
                用推理路径检索: 将这个生成的推理路径作为Query，去训练集中检索与之语义最相似的k个样本作为演示 。
                
                
                迭代优化: 用上一步选出的演示和原始样本再次提问{LLM}，生成新的答案和新的推理路径。然后，用这个新的推理路径重复第2步，进行下一轮的演示选择 。
                
                
                投票得出结果: 迭代数次后，对每次生成的答案进行多数投票，得到最终结果 。
                
                
                
                
                },
  file       = {PDF:files/526/Qin 等 - 2024 - In-Context Learning with Iterative Demonstration Selection.pdf:application/pdf}
}

@misc{rodrigues_meta-prompting_2024,
  title      = {Meta-prompting Optimized Retrieval-augmented Generation},
  url        = {http://arxiv.org/abs/2407.03955},
  doi        = {10.48550/arXiv.2407.03955},
  abstract   = {Retrieval-augmented generation resorts to content retrieved from external sources in order to leverage the performance of large language models in downstream tasks. The excessive volume of retrieved content, the possible dispersion of its parts, or their out of focus range may happen nevertheless to eventually have a detrimental rather than an incremental eﬀect. To mitigate this issue and improve retrieval-augmented generation, we propose a method to reﬁne the retrieved content before it is included in the prompt by resorting to meta-prompting optimization. Put to empirical test with the demanding multi-hop question answering task from the {StrategyQA} dataset, the evaluation results indicate that this method outperforms a similar retrieval-augmented system but without this method by over 30 \%.},
  number     = {{arXiv}:2407.03955},
  publisher  = {{arXiv}},
  author     = {Rodrigues, João and Branco, António},
  urldate    = {2025-06-20},
  year       = {2024-07-04},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2407.03955 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                这篇论文关注如何优化检索增强生成（{RAG}）的性能。它认为从外部检索到的内容可能冗长、杂乱，直接拼接到Prompt中效果不佳 。因此，需要一个步骤来**“精炼”（refine）**这些检索到的内容。而精炼内容所用的“指令”本身，也可以通过{LLM进行自动优化}。
                
                
                
                主要方法:
                
                
                三{LLM架构}:
                
                
                生成{LLM} (generation-{LLM}): 执行最终的问答任务。
                
                
                转换{LLM} (transformation-{LLM}): 负责接收检索到的原始文档和一条“精炼指令”，然后输出精炼后的内容（如摘要、核心观点等）。
                
                
                优化器{LLM} (optimizer-{LLM}): 它的任务是生成并优化给转换{LLM的那条}“精炼指令”。
                
                
                
                
                元提示（Meta-prompting）: 优化器{LLM通过一个}“元提示”来工作。这个元提示里包含了“请你生成一条更好的精炼指令”的元指令，以及一个当前最优的精炼指令列表（附带它们历史表现的分数）。优化器{LLM会参考这些信息}，生成新的、可能更好的精炼指令。这个过程可以迭代进行，不断发现最优的精炼指令 。
                
                
                
                
                },
  file       = {PDF:files/530/Rodrigues和Branco - 2024 - Meta-prompting Optimized Retrieval-augmented Generation.pdf:application/pdf}
}

@misc{villena_llmner_2024,
  title      = {{llmNER}: (Zero{\textbar}Few)-Shot Named Entity Recognition, Exploiting the Power of Large Language Models},
  url        = {http://arxiv.org/abs/2406.04528},
  doi        = {10.48550/arXiv.2406.04528},
  shorttitle = {{llmNER}},
  abstract   = {Large language models ({LLMs}) allow us to generate high-quality human-like text. One interesting task in natural language processing ({NLP}) is named entity recognition ({NER}), which seeks to detect mentions of relevant information in documents. This paper presents {llmNER}, a Python library for implementing zero-shot and few-shot {NER} with {LLMs}; by providing an easyto-use interface, {llmNER} can compose prompts, query the model, and parse the completion returned by the {LLM}. Also, the library enables the user to perform prompt engineering efficiently by providing a simple interface to test multiple variables. We validated our software on two {NER} tasks to show the library’s flexibility. {llmNER} aims to push the boundaries of in-context learning research by removing the barrier of the prompting and parsing steps.},
  number     = {{arXiv}:2406.04528},
  publisher  = {{arXiv}},
  author     = {Villena, Fabián and Miranda, Luis and Aracena, Claudio},
  urldate    = {2025-06-20},
  year       = {2024-06-06},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2406.04528 [cs]},
  keywords   = {Computer Science - Computation and Language},
  file       = {PDF:files/571/Villena 等 - 2024 - llmNER (ZeroFew)-Shot Named Entity Recognition, Exploiting the Power of Large Language Models.pdf:application/pdf}
}

@misc{wang_cooperative_2025,
  title      = {A Cooperative Multi-Agent Framework for Zero-Shot Named Entity Recognition},
  url        = {http://arxiv.org/abs/2502.18702},
  doi        = {10.48550/arXiv.2502.18702},
  abstract   = {Zero-shot named entity recognition ({NER}) aims to develop entity recognition systems from unannotated text corpora. This task presents substantial challenges due to minimal human intervention. Recent work has adapted large language models ({LLMs}) for zero-shot {NER} by crafting specialized prompt templates. It advances model self-learning abilities by incorporating self-annotated demonstrations. However, two important challenges persist: (i) Correlations between contexts surrounding entities are overlooked, leading to wrong type predictions or entity omissions. (ii) The indiscriminate use of task demonstrations, retrieved through shallow similarity-based strategies, severely misleads {LLMs} during inference. In this paper, we introduce the cooperative multi-agent system ({CMAS}), a novel framework for zero-shot {NER} that uses the collective intelligence of multiple agents to address the challenges outlined above. {CMAS} has four main agents: (i) a self-annotator, (ii) a type-related feature ({TRF}) extractor, (iii) a demonstration discriminator, and (iv) an overall predictor. To explicitly capture correlations between contexts surrounding entities, {CMAS} reformulates {NER} into two subtasks: recognizing named entities and identifying entity type-related features within the target sentence. To enable controllable utilization of demonstrations, a demonstration discriminator is established to incorporate the self-reflection mechanism, automatically evaluating helpfulness scores for the target sentence. Experimental results show that {CMAS} significantly improves zero-shot {NER} performance across six benchmarks, including both domain-specific and general-domain scenarios. Furthermore, {CMAS} demonstrates its effectiveness in few-shot settings and with various {LLM} backbones.},
  number     = {{arXiv}:2502.18702},
  publisher  = {{arXiv}},
  author     = {Wang, Zihan and Zhao, Ziqi and Lyu, Yougang and Chen, Zhumin and Rijke, Maarten de and Ren, Zhaochun},
  urldate    = {2025-06-20},
  year       = {2025-02-25},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2502.18702 [cs]},
  keywords   = {Computer Science - Computation and Language, Computer Science - Information Retrieval},
  annotation = {
                
                这篇论文将Zero-shot {NER任务构建为一个多智能体协作系统}（Cooperative Multi-Agent System, {CMAS}） 。它认为单个{LLM通过简单的Prompt难以解决复杂的NER问题}，因此设计了多个各司其职的Agent，通过它们的协作来提升整体性能 。
                
                
                
                主要方法 (四Agent协作):
                
                
                自标注器 (Self-annotator): 负责在无标签数据上进行初步的实体标注，创建一个伪标签数据集 。
                
                
                类型相关特征提取器 ({TRF} Extractor): 重新定义了{NER任务}，不仅要识别实体，还要识别与实体类型强相关的上下文特征（{TRF}），以解决上下文关联被忽略的问题 。
                
                
                示例判别器 (Demonstration Discriminator): 引入自反思（Self-reflection）机制，在推理时，它会评估检索到的每个示例对于当前测试样本的“有效性得分”，避免被不相关的示例误导 。
                
                
                总预测器 (Overall Predictor): 综合所有信息（{TRF}、示例的有效性得分等），做出最终的{NER预测} 。
                
                
                
                
                
                
                
                },
  annotation = {Comment: Accepted at {WWW} 2025},
  annotation = {Comment: Accepted at {WWW} 2025},
  file       = {PDF:files/532/Wang 等 - 2025 - A Cooperative Multi-Agent Framework for Zero-Shot Named Entity Recognition.pdf:application/pdf}
}

@misc{wang_s-prompts_2023,
  title      = {S-Prompts Learning with Pre-trained Transformers: An Occam's Razor for Domain Incremental Learning},
  url        = {http://arxiv.org/abs/2207.12819},
  doi        = {10.48550/arXiv.2207.12819},
  shorttitle = {S-Prompts Learning with Pre-trained Transformers},
  abstract   = {State-of-the-art deep neural networks are still struggling to address the catastrophic forgetting problem in continual learning. In this paper, we propose one simple paradigm (named as S-Prompting) and two concrete approaches to highly reduce the forgetting degree in one of the most typical continual learning scenarios, i.e., domain increment learning ({DIL}). The key idea of the paradigm is to learn prompts independently across domains with pre-trained transformers, avoiding the use of exemplars that commonly appear in conventional methods. This results in a win-win game where the prompting can achieve the best for each domain. The independent prompting across domains only requests one single cross-entropy loss for training and one simple K-{NN} operation as a domain identiﬁer for inference. The learning paradigm derives an image prompt learning approach and a novel language-image prompt learning approach. Owning an excellent scalability (0.03\% parameter increase per domain), the best of our approaches achieves a remarkable relative improvement (an average of about 30\%) over the best of the state-of-the-art exemplar-free methods for three standard {DIL} tasks, and even surpasses the best of them relatively by about 6\% in average when they use exemplars. Source code is available at https://github.com/iamwangyabin/S-Prompts.},
  number     = {{arXiv}:2207.12819},
  publisher  = {{arXiv}},
  author     = {Wang, Yabin and Huang, Zhiwu and Hong, Xiaopeng},
  urldate    = {2025-06-20},
  year       = {2023-03-18},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2207.12819 [cs]},
  keywords   = {Computer Science - Machine Learning, Computer Science - Computer Vision and Pattern Recognition},
  annotation = {Comment: Accepted to {NeurIPS} 2022},
  file       = {PDF:files/570/Wang 等 - 2023 - S-Prompts Learning with Pre-trained Transformers An Occam's Razor for Domain Incremental Learning.pdf:application/pdf}
}

@misc{wang2023gptnernamedentityrecognition,
  title         = {GPT-NER: Named Entity Recognition via Large Language Models},
  author        = {Shuhe Wang and Xiaofei Sun and Xiaoya Li and Rongbin Ouyang and Fei Wu and Tianwei Zhang and Jiwei Li and Guoyin Wang},
  year          = {2023},
  eprint        = {2304.10428},
  archiveprefix = {arXiv},
  primaryclass  = {cs.CL},
  url           = {https://arxiv.org/abs/2304.10428},
  annotation    = {GPT-NER 框架
                   为了解决上述问题，作者提出了 GPT-NER 框架。其核心思想是通过一系列巧妙的设计，将NER任务彻底改造成一个LLM擅长的文本生成任务。
                   
                   该框架主要包含以下几个关键部分：
                   
                   1. 任务形式转换 (Transforming the Task Format)
                   
                   创新点: 作者没有让LLM输出B-I-O标签或者JSON对象，而是设计了一种新颖的**“原文改写”**格式。
                   具体做法: 要求LLM生成与输入句子几乎完全相同的文本，但用**特殊的分隔符（@@ 和 ##）**将识别出的实体包裹起来。
                   例如，要从句子 Columbus is a city 中识别“地点”实体，LLM需要生成 @@Columbus## is a city。
                   优势: 这种方式极大地降低了LLM的生成难度。模型不需要学习复杂的对齐或生成全新的结构，只需在原文基础上“复制粘贴”并“添加标记”，从而能更好地适应任务。
                   2. 示例检索 (Few-shot Demonstration Retrieval)
                   
                   为了给LLM提供高质量的上下文示例（demonstrations），作者比较了三种检索策略：
                   随机检索 (Random Retrieval): 随机从训练集中挑选示例，效果最差。
                   句子级kNN检索 (Sentence-level kNN): 检索与输入句子语义最相似的句子作为示例。效果有提升，但可能找到的句子虽然意思相近，却不包含关键的实体信息。
                   实体级kNN检索 (Entity-level kNN): 这是作者推荐的方法。先用一个预训练好的NER模型粗略地识别出输入句子中的潜在实体，然后用这些实体的表示（embedding）去检索训练集中包含相似实体的句子。这种方法能找到与任务最相关的示例，效果最好。
                   3. 自我验证机制 (Self-verification Strategy)
                   
                   动机: 解决LLM在NER任务中常见的**“幻觉”或过度预测**问题（即把不是实体的内容也识别为实体）。
                   创新点: 设计了一个两阶段流程。在第一阶段用上述方法抽取实体后，进入第二阶段。
                   具体做法: 在第二阶段，系统会向LLM发起一个新的、验证性的提问。例如，如果第一阶段将"Hendrix"识别为了“地点”，第二阶段就会问：“在句子‘Rare Hendrix song sells for $17’中，‘Hendrix’是一个地点实体吗？请用‘是’或‘否’回答。”
                   优势: 通过这种“自问自答”的方式，LLM可以对自己的初步判断进行二次审查，有效过滤掉许多错误的识别结果，显著提升了准确率（Precision）。
                   实验与主要发现
                   性能卓越: GPT-NER在多个NER数据集（包括扁平NER和嵌套NER）上，取得了与全监督基线模型**相当（comparable）**的性能，这是首次有研究在使用LLM的ICL方法上达到如此高度。
                   低资源优势: 在训练数据极度稀少（few-shot/low-resource）的情况下，GPT-NER的性能显著优于传统的监督模型。例如，在只有8个训练样本时，GPT-NER的F1分数已达到约60，而监督模型几乎为0。}
}

@misc{xie_empirical_2023,
  title      = {Empirical Study of Zero-Shot {NER} with {ChatGPT}},
  url        = {http://arxiv.org/abs/2310.10035},
  doi        = {10.48550/arXiv.2310.10035},
  abstract   = {Large language models ({LLMs}) exhibited powerful capability in various natural language processing tasks. This work focuses on exploring {LLM} performance on zero-shot information extraction, with a focus on the {ChatGPT} and named entity recognition ({NER}) task. Inspired by the remarkable reasoning capability of {LLM} on symbolic and arithmetic reasoning, we adapt the prevalent reasoning methods to {NER} and propose reasoning strategies tailored for {NER}. First, we explore a decomposed question-answering paradigm by breaking down the {NER} task into simpler subproblems by labels. Second, we propose syntactic augmentation to stimulate the model's intermediate thinking in two ways: syntactic prompting, which encourages the model to analyze the syntactic structure itself, and tool augmentation, which provides the model with the syntactic information generated by a parsing tool. Besides, we adapt self-consistency to {NER} by proposing a two-stage majority voting strategy, which first votes for the most consistent mentions, then the most consistent types. The proposed methods achieve remarkable improvements for zero-shot {NER} across seven benchmarks, including Chinese and English datasets, and on both domain-specific and general-domain scenarios. In addition, we present a comprehensive analysis of the error types with suggestions for optimization directions. We also verify the effectiveness of the proposed methods on the few-shot setting and other {LLMs}.},
  number     = {{arXiv}:2310.10035},
  publisher  = {{arXiv}},
  author     = {Xie, Tingyu and Li, Qi and Zhang, Jian and Zhang, Yan and Liu, Zuozhu and Wang, Hongwei},
  urldate    = {2025-06-20},
  year       = {2023-10-16},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2310.10035 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                这篇论文系统地研究了如何激发{ChatGPT在零样本}（Zero-Shot）{NER任务中的}“推理能力”。它认为，不能简单地直接要求模型输出结果，而应该通过更精巧的提示工程（Prompting）来引导模型进行“思考” 。
                
                
                
                主要方法:
                
                
                分解式问答 (Decomposed-{QA}): 将一次性抽取所有实体类型的复杂任务，分解成一个多轮对话过程。每一轮只要求模型抽取一种实体类型，并将之前的问答作为上下文 。这降低了任务的复杂度。
                
                
                句法增强 (Syntactic Augmentation): 通过两种方式为模型提供句法信息，引导其思考 。
                
                
                句法提示 (Syntactic Prompting): 在Prompt中加入提示语，鼓励模型先自行分析句法结构，再进行识别 。
                
                
                工具增强 (Tool Augmentation): 直接使用外部解析工具（如Hanlp）生成词性标注、依存树等信息，并将其作为输入提供给模型 。
                
                
                
                
                两阶段多数投票 (Two-Stage Majority Voting): 针对自洽性（Self-Consistency）的一种优化策略。在采样多个答案后，第一阶段先对实体本身（mention）进行投票，第二阶段再对实体类型（type）进行投票 。
                
                
                
                
                },
  annotation = {Comment: Accepted to {EMNLP} 2023 (Main Conference)},
  annotation = {Comment: Accepted to {EMNLP} 2023 (Main Conference)},
  file       = {PDF:files/533/Xie 等 - 2023 - Empirical Study of Zero-Shot NER with ChatGPT.pdf:application/pdf}
}

@misc{xie_retrieval_2024,
  title      = {Retrieval Augmented Instruction Tuning for Open {NER} with Large Language Models},
  url        = {http://arxiv.org/abs/2406.17305},
  doi        = {10.48550/arXiv.2406.17305},
  abstract   = {The strong capability of large language models ({LLMs}) has been applied to information extraction ({IE}) through either retrieval augmented prompting or instruction tuning ({IT}). However, the best way to incorporate information with {LLMs} for {IE} remains an open question. In this paper, we explore Retrieval Augmented Instruction Tuning ({RA}-{IT}) for {IE}, focusing on the task of open named entity recognition ({NER}). Specifically, for each training sample, we retrieve semantically similar examples from the training dataset as the context and prepend them to the input of the original instruction. To evaluate our {RA}-{IT} approach more thoroughly, we construct a Chinese {IT} dataset for open {NER} and evaluate {RA}-{IT} in both English and Chinese scenarios. Experimental results verify the effectiveness of {RA}-{IT} across various data sizes and in both English and Chinese scenarios. We also conduct thorough studies to explore the impacts of various retrieval strategies in the proposed {RA}-{IT} framework. Code and data are available at: https://github.com/Emma1066/Retrieval-Augmented-{IT}-{OpenNER}},
  number     = {{arXiv}:2406.17305},
  publisher  = {{arXiv}},
  author     = {Xie, Tingyu and Zhang, Jian and Zhang, Yan and Liang, Yuanyuan and Li, Qi and Wang, Hongwei},
  urldate    = {2025-06-20},
  year       = {2024-12-01},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2406.17305 [cs]},
  keywords   = {Computer Science - Computation and Language},
  annotation = {
                
                这篇论文的核心观点是，在对模型进行指令微调（Instruction Tuning）时，仅仅使用原始指令是不够的。如果在每个训练样本的指令前，先从训练集中**检索(Retrieval)**出语义相似的样本作为上下文，可以显著提升模型在开放域命名实体识别（Open {NER}）任务上的性能 。他们将这种方法称为 {RA}-{IT} (Retrieval Augmented - Instruction Tuning) 。
                
                
                
                主要方法:
                
                
                对于每一个训练样本，使用基于句子嵌入的检索器（如{GTE}-large）在整个训练数据集中找到k个语义最相似的样本 。
                
                
                将这k个检索到的样本（作为上下文）拼接到原始的指令微调模板之前 。
                
                
                使用这种“增强后”的数据对大语言模型（{LLaMA}-3, Qwen-1.5）进行微调 。
                
                
                
                
                },
  annotation = {Comment: To be appeared at {COLING} 2025},
  annotation = {Comment: To be appeared at {COLING} 2025},
  file       = {PDF:files/537/Xie 等 - 2024 - Retrieval Augmented Instruction Tuning for Open NER with Large Language Models.pdf:application/pdf}
}

@inproceedings{zhang-etal-2023-2iner,
  title      = {2{INER}: Instructive and In-Context Learning on Few-Shot Named Entity Recognition},
  author     = {Zhang, Jiasheng  and
                Liu, Xikai  and
                Lai, Xinyi  and
                Gao, Yan  and
                Wang, Shusen  and
                Hu, Yao  and
                Lin, Yiqing},
  editor     = {Bouamor, Houda  and
                Pino, Juan  and
                Bali, Kalika},
  booktitle  = {Findings of the Association for Computational Linguistics: EMNLP 2023},
  month      = dec,
  year       = {2023},
  address    = {Singapore},
  publisher  = {Association for Computational Linguistics},
  url        = {https://aclanthology.org/2023.findings-emnlp.259/},
  doi        = {10.18653/v1/2023.findings-emnlp.259},
  pages      = {3940--3951},
  abstract   = {Prompt-based learning has emerged as a powerful technique in natural language processing (NLP) due to its ability to leverage pre-training knowledge for downstream few-shot tasks. In this paper, we propose 2INER, a novel text-to-text framework for Few-Shot Named Entity Recognition (NER) tasks. Our approach employs instruction finetuning based on InstructionNER to enable the model to effectively comprehend and process task-specific instructions, including both main and auxiliary tasks. We also introduce a new auxiliary task, called Type Extracting, to enhance the model{'}s understanding of entity types in the overall semantic context of a sentence. To facilitate in-context learning, we concatenate examples to the input, enabling the model to learn from additional contextual information. Experimental results on four datasets demonstrate that our approach outperforms existing Few-Shot NER methods and remains competitive with state-of-the-art standard NER algorithms.},
  annotation = {该论文旨在解决少样本命名实体识别（Few-Shot NER） 任务中的挑战，即在只有少量标注数据的情况下有效识别实体 。作者认为，虽然基于指令微调（instruction finetuning）的方法如InstructionNER已经取得了进展，但仍存在两个可提升的空间：一是模型对实体类型在句子全局语义下的理解不够深入；二是未能利用上下文学习（In-Context Learning, ICL）中通过示例提供额外知识的优势 。
                为此，论文提出了一个名为 2INER 的新框架，其核心是将指令微调和上下文学习两种技术融合在一起，以充分发掘预训练语言模型（PLM）的潜力 。
                方法论详解：2INER的两大创新
                2INER框架建立在InstructionNER的文本到文本（text-to-text）范式之上 ，通过引入一个新的辅助任务和加入上下文示例来增强模型能力。
                1. 新的辅助任务：类型抽取 (Type Extraction)
                动机: 作者认为，一个完整的NER过程可以分解为三步：理解标签与句子语义的关系、识别实体边界（span recognition）、为边界标注类型（entity labeling）。InstructionNER的辅助任务主要关注后两步，而2INER则旨在补强第一步 。
                任务定义: “类型抽取”任务要求模型在不关注具体实体边界的情况下，直接判断出给定句子中包含了哪些实体类型 。例如，对于句子“are there any cheap 4 star restaurants near here”，模型需要输出“rating type exists in the sentence”、“location type exists in the sentence”等 。
                目的与效果: 这个任务迫使模型从句子的整体语义层面去理解实体类型的含义，而不是仅仅依赖于某个词或短语 。作者认为，加深了对标签语义的理解后，模型在识别实体边界和进行实体标注时的能力都会得到同步提升 。
                2. 融入上下文学习 (In-Context Learning)
                动机: 利用ICL的优势，为模型提供额外的上下文信息以增强其少样本学习能力 。
                实施方式:
                在主NER任务的输入提示（prompt）中，除了任务指令、待处理句子和候选标签列表外，还拼接了一个“示例（Examples）”字段 。
                这些示例以自然语言的形式呈现，例如“‘modills’ is a restaurant name”，直观地向模型展示了哪种文本片段对应哪种实体类型 。
                为了避免信息泄露，推理时使用的示例从训练支持集（training support set）中采样 。在训练阶段，为了保证覆盖面，会为候选列表中的每种实体类型都提供至少一个示例 。}
}

@misc{zhang2024incontextlearningfewshotnested,
  title         = {In-Context Learning for Few-Shot Nested Named Entity Recognition},
  author        = {Meishan Zhang and Bin Wang and Hao Fei and Min Zhang},
  year          = {2024},
  eprint        = {2402.01182},
  archiveprefix = {arXiv},
  primaryclass  = {cs.CL},
  url           = {https://arxiv.org/abs/2402.01182},
  annotation    = {该论文提出了一个专为少样本嵌套NER设计的、基于ICL的先进框架。其核心贡献是创新的EnDe Retriever，
                   
                   EnDe Retriever的创新之处在于，它通过对比学习来优化检索过程，综合考量以下三个维度的相似性：
                   
                   
                   
                   语义相似性 (Semantic Similarity)：确保检索到的示例在句子语义上与测试样本相近 。
                   
                   边界相似性 (Boundary Similarity)：这是针对嵌套NER的关键创新。通过引入词性标注 (POS tags) 和句法成分树 (constituency trees) 等特征，让边界结构相似的句子在表示空间中更接近，从而为模型提供更好的实体边界线索 。
                   
                   标签相似性 (Label Identification)：通过对比学习，拉近具有相同实体标签的示例，同时推开那些文本有重叠但标签不同的实体示例，以减少歧义
                   
                   它通过一个结合了语义、边界和标签三个维度的对比学习机制，来挑选高质量的示例。实验证明，这种精细化的示例选择策略非常有效，使得模型在极少量标注数据的情况下}
}


@misc{zhao_dynamic_2024,
  title      = {Dynamic Demonstrations Controller for In-Context Learning},
  url        = {http://arxiv.org/abs/2310.00385},
  doi        = {10.48550/arXiv.2310.00385},
  abstract   = {In-context learning ({ICL}) is a new paradigm for natural language processing ({NLP}), where a large language model ({LLM}) observes a small number of demonstrations and a test instance as its input, and directly makes predictions without updating model parameters. Previous studies have revealed that {ICL} is sensitive to the selection and the ordering of demonstrations. However, there are few studies regarding the impact of the demonstration number on the {ICL} performance within a limited input length of {LLM}, because it is commonly believed that the number of demonstrations is positively correlated with model performance. In this paper, we found this conclusion does not always hold true. Through pilot experiments, we discover that increasing the number of demonstrations does not necessarily lead to improved performance. Building upon this insight, we propose a Dynamic Demonstrations Controller (D2Controller), which can improve the {ICL} performance by adjusting the number of demonstrations dynamically. The experimental results show that D2Controller yields a 4.6\% relative improvement on ten different sizes of {LLMs} across ten datasets. Besides, we also extend our method to previous {ICL} models and achieve competitive results.},
  number     = {{arXiv}:2310.00385},
  publisher  = {{arXiv}},
  author     = {Zhao, Fei and Pang, Taotian and Wu, Zhen and Ma, Zheng and Huang, Shujian and Dai, Xinyu},
  urldate    = {2025-06-20},
  year       = {2024-12-11},
  langid     = {english},
  eprinttype = {arxiv},
  eprint     = {2310.00385 [cs]},
  keywords   = {Computer Science - Artificial Intelligence, Computer Science - Computation and Language},
  file       = {PDF:files/568/Zhao 等 - 2024 - Dynamic Demonstrations Controller for In-Context Learning.pdf:application/pdf}
}



@misc{rae2022scalinglanguagemodelsmethods,
      title={Scaling Language Models: Methods, Analysis & Insights from Training Gopher}, 
      author={Jack W. Rae and Sebastian Borgeaud and Trevor Cai and Katie Millican and Jordan Hoffmann and Francis Song and John Aslanides and Sarah Henderson and Roman Ring and Susannah Young and Eliza Rutherford and Tom Hennigan and Jacob Menick and Albin Cassirer and Richard Powell and George van den Driessche and Lisa Anne Hendricks and Maribeth Rauh and Po-Sen Huang and Amelia Glaese and Johannes Welbl and Sumanth Dathathri and Saffron Huang and Jonathan Uesato and John Mellor and Irina Higgins and Antonia Creswell and Nat McAleese and Amy Wu and Erich Elsen and Siddhant Jayakumar and Elena Buchatskaya and David Budden and Esme Sutherland and Karen Simonyan and Michela Paganini and Laurent Sifre and Lena Martens and Xiang Lorraine Li and Adhiguna Kuncoro and Aida Nematzadeh and Elena Gribovskaya and Domenic Donato and Angeliki Lazaridou and Arthur Mensch and Jean-Baptiste Lespiau and Maria Tsimpoukelli and Nikolai Grigorev and Doug Fritz and Thibault Sottiaux and Mantas Pajarskas and Toby Pohlen and Zhitao Gong and Daniel Toyama and Cyprien de Masson d'Autume and Yujia Li and Tayfun Terzi and Vladimir Mikulik and Igor Babuschkin and Aidan Clark and Diego de Las Casas and Aurelia Guy and Chris Jones and James Bradbury and Matthew Johnson and Blake Hechtman and Laura Weidinger and Iason Gabriel and William Isaac and Ed Lockhart and Simon Osindero and Laura Rimell and Chris Dyer and Oriol Vinyals and Kareem Ayoub and Jeff Stanway and Lorrayne Bennett and Demis Hassabis and Koray Kavukcuoglu and Geoffrey Irving},
      year={2022},
      eprint={2112.11446},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2112.11446}, 
}

@misc{smith2022usingdeepspeedmegatrontrain,
      title={Using DeepSpeed and Megatron to Train Megatron-Turing NLG 530B, A Large-Scale Generative Language Model}, 
      author={Shaden Smith and Mostofa Patwary and Brandon Norick and Patrick LeGresley and Samyam Rajbhandari and Jared Casper and Zhun Liu and Shrimai Prabhumoye and George Zerveas and Vijay Korthikanti and Elton Zhang and Rewon Child and Reza Yazdani Aminabadi and Julie Bernauer and Xia Song and Mohammad Shoeybi and Yuxiong He and Michael Houston and Saurabh Tiwary and Bryan Catanzaro},
      year={2022},
      eprint={2201.11990},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2201.11990}, 
}

@misc{raffel2023exploringlimitstransferlearning,
      title={Exploring the Limits of Transfer Learning with a Unified Text-to-Text Transformer}, 
      author={Colin Raffel and Noam Shazeer and Adam Roberts and Katherine Lee and Sharan Narang and Michael Matena and Yanqi Zhou and Wei Li and Peter J. Liu},
      year={2023},
      eprint={1910.10683},
      archivePrefix={arXiv},
      primaryClass={cs.LG},
      url={https://arxiv.org/abs/1910.10683}, 
}

@misc{roberts2020knowledgepackparameterslanguage,
      title={How Much Knowledge Can You Pack Into the Parameters of a Language Model?}, 
      author={Adam Roberts and Colin Raffel and Noam Shazeer},
      year={2020},
      eprint={2002.08910},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2002.08910}, 
}

@misc{guu2020realmretrievalaugmentedlanguagemodel,
      title={REALM: Retrieval-Augmented Language Model Pre-Training}, 
      author={Kelvin Guu and Kenton Lee and Zora Tung and Panupong Pasupat and Ming-Wei Chang},
      year={2020},
      eprint={2002.08909},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2002.08909},
}

@misc{wang_gpt-ner_2023,
  title         = {GPT-NER: Named Entity Recognition via Large Language Models},
  author        = {Shuhe Wang and Xiaofei Sun and Xiaoya Li and Rongbin Ouyang and Fei Wu and Tianwei Zhang and Jiwei Li and Guoyin Wang},
  year          = {2023},
  eprint        = {2304.10428},
  archiveprefix = {arXiv},
  primaryclass  = {cs.CL},
  url           = {https://arxiv.org/abs/2304.10428}
}

@misc{adiga2024designinginformativemetricsfewshot,
      title={Designing Informative Metrics for Few-Shot Example Selection}, 
      author={Rishabh Adiga and Lakshminarayanan Subramanian and Varun Chandrasekaran},
      year={2024},
      eprint={2403.03861},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2403.03861}, 
}

@misc{bai2025labelguidedincontextlearningnamed,
      title={Label-Guided In-Context Learning for Named Entity Recognition}, 
      author={Fan Bai and Hamid Hassanzadeh and Ardavan Saeedi and Mark Dredze},
      year={2025},
      eprint={2505.23722},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2505.23722}, 
}

@misc{adiga2024designinginformativemetricsfewshot,
      title={Designing Informative Metrics for Few-Shot Example Selection}, 
      author={Rishabh Adiga and Lakshminarayanan Subramanian and Varun Chandrasekaran},
      year={2024},
      eprint={2403.03861},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2403.03861}, 
}

@misc{liu2021makesgoodincontextexamples,
      title={What Makes Good In-Context Examples for GPT-$3$?}, 
      author={Jiachang Liu and Dinghan Shen and Yizhe Zhang and Bill Dolan and Lawrence Carin and Weizhu Chen},
      year={2021},
      eprint={2101.06804},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2101.06804}, 
}