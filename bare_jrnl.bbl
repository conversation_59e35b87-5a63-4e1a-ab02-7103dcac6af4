\begin{thebibliography}{7}
\providecommand{\natexlab}[1]{#1}
\providecommand{\url}[1]{\texttt{#1}}
\expandafter\ifx\csname urlstyle\endcsname\relax
  \providecommand{\doi}[1]{doi: #1}\else
  \providecommand{\doi}{doi: \begingroup \urlstyle{rm}\Url}\fi

\bibitem[Adiga et~al.(2024-07-30)Adiga, Subramanian, and Chandrasekaran]{adiga_designing_2024}
<PERSON><PERSON><PERSON><PERSON>, Lakshminarayanan Subramanian, and <PERSON><PERSON><PERSON>.
\newblock Designing informative metrics for few-shot example selection, 2024-07-30.
\newblock URL \url{http://arxiv.org/abs/2403.03861}.

\bibitem[Ashok and Lipton(2023-06-20)]{ashok_promptner_2023}
Dhana<PERSON>y <PERSON> and <PERSON>~C. Lipton.
\newblock {PromptNER}: Prompting for named entity recognition, 2023-06-20.
\newblock URL \url{http://arxiv.org/abs/2305.15444}.

\bibitem[<PERSON><PERSON><PERSON> et~al.(2024-12-20)<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>]{keraghel_recent_2024}
Imed <PERSON>raghel, <PERSON>islas <PERSON>rbieu, and <PERSON> Nadif.
\newblock Recent advances in named entity recognition: A comprehensive survey and comparative study, 2024-12-20.
\newblock URL \url{http://arxiv.org/abs/2401.10825}.

\bibitem[Min et~al.(2022-10-20)Min, Lyu, Holtzman, Artetxe, Lewis, Hajishirzi, and Zettlemoyer]{min_rethinking_2022}
Sewon Min, Xinxi Lyu, Ari Holtzman, Mikel Artetxe, Mike Lewis, Hannaneh Hajishirzi, and Luke Zettlemoyer.
\newblock Rethinking the role of demonstrations: What makes in-context learning work?, 2022-10-20.
\newblock URL \url{http://arxiv.org/abs/2202.12837}.

\bibitem[Qin et~al.(2024-12-30)Qin, Zhang, Chen, Dagar, and Ye]{qin_-context_2024}
Chengwei Qin, Aston Zhang, Chen Chen, Anirudh Dagar, and Wenming Ye.
\newblock In-context learning with iterative demonstration selection, 2024-12-30.
\newblock URL \url{http://arxiv.org/abs/2310.09881}.

\bibitem[Rodrigues and Branco(2024-07-04)]{rodrigues_meta-prompting_2024}
João Rodrigues and António Branco.
\newblock Meta-prompting optimized retrieval-augmented generation, 2024-07-04.
\newblock URL \url{http://arxiv.org/abs/2407.03955}.

\bibitem[Wang et~al.(2023)Wang, Sun, Li, Ouyang, Wu, Zhang, Li, and Wang]{wang_gpt-ner_2023}
Shuhe Wang, Xiaofei Sun, Xiaoya Li, Rongbin Ouyang, Fei Wu, Tianwei Zhang, Jiwei Li, and Guoyin Wang.
\newblock Gpt-ner: Named entity recognition via large language models, 2023.
\newblock URL \url{https://arxiv.org/abs/2304.10428}.

\end{thebibliography}
