#!/usr/bin/env python3
"""
NER知识库更新助手
用于自动化处理bib文件更新和知识库维护
"""

import json
import re
import hashlib
from pathlib import Path
from typing import Dict, List, Set, Tuple
from datetime import datetime

class NERKnowledgeBaseUpdater:
    def __init__(self, config_file: str = "knowledge_base_config.json"):
        """初始化更新器"""
        self.config_file = config_file
        self.config = self.load_config()
        
    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"配置文件 {self.config_file} 不存在")
            return {}
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def parse_bib_file(self, bib_file: str) -> Dict[str, Dict]:
        """解析BibTeX文件，提取所有条目"""
        entries = {}
        
        try:
            with open(bib_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except FileNotFoundError:
            print(f"BibTeX文件 {bib_file} 不存在")
            return {}
        
        # 简单的BibTeX解析（可以用更专业的库如pybtex替代）
        pattern = r'@(\w+)\{([^,]+),\s*\n(.*?)\n\}'
        matches = re.findall(pattern, content, re.DOTALL)
        
        for entry_type, key, fields_text in matches:
            fields = {}
            # 解析字段
            field_pattern = r'(\w+)\s*=\s*\{([^}]*)\}'
            field_matches = re.findall(field_pattern, fields_text)
            
            for field_name, field_value in field_matches:
                fields[field_name.lower()] = field_value.strip()
            
            entries[key] = {
                'type': entry_type.lower(),
                'key': key,
                **fields
            }
        
        return entries
    
    def get_file_hash(self, file_path: str) -> str:
        """获取文件的MD5哈希值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except FileNotFoundError:
            return ""
    
    def check_bib_updates(self, bib_file: str = "references.bib") -> Tuple[Set[str], Set[str]]:
        """检查bib文件是否有更新，返回新增和删除的条目"""
        current_hash = self.get_file_hash(bib_file)
        last_hash = self.config.get('update_tracking', {}).get('last_bib_file_hash')
        
        if current_hash == last_hash:
            print("BibTeX文件没有变化")
            return set(), set()
        
        # 解析当前bib文件
        current_entries = self.parse_bib_file(bib_file)
        current_keys = set(current_entries.keys())
        
        # 获取已知的条目
        known_keys = set(self.config.get('key_methods', {}).keys())
        
        # 找出新增和删除的条目
        new_keys = current_keys - known_keys
        deleted_keys = known_keys - current_keys
        
        # 更新配置
        self.config.setdefault('update_tracking', {})['last_bib_file_hash'] = current_hash
        
        return new_keys, deleted_keys
    
    def extract_paper_info(self, bib_entry: Dict) -> Dict:
        """从bib条目提取论文信息"""
        info = {
            'title': bib_entry.get('title', ''),
            'authors': bib_entry.get('author', ''),
            'year': bib_entry.get('year', ''),
            'journal': bib_entry.get('journal', ''),
            'booktitle': bib_entry.get('booktitle', ''),
            'url': bib_entry.get('url', ''),
            'doi': bib_entry.get('doi', ''),
            'arxiv': bib_entry.get('eprint', '')
        }
        
        # 清理作者名称
        if info['authors']:
            # 简单处理，去掉多余空格和换行
            info['authors'] = re.sub(r'\s+', ' ', info['authors']).strip()
            # 如果作者太多，只保留前几个
            if len(info['authors']) > 100:
                info['authors'] = info['authors'][:100] + "..."
        
        return info
    
    def generate_search_queries(self, paper_info: Dict) -> List[str]:
        """为论文生成搜索查询"""
        queries = []
        
        title = paper_info.get('title', '')
        authors = paper_info.get('authors', '')
        year = paper_info.get('year', '')
        
        if title:
            # 提取标题中的关键词
            title_clean = re.sub(r'[^\w\s]', ' ', title)
            title_words = [w for w in title_clean.split() if len(w) > 3]
            
            if len(title_words) >= 3:
                # 使用前几个关键词
                key_terms = ' '.join(title_words[:4])
                queries.append(f'"{key_terms}" {year}')
            
            # 完整标题搜索
            queries.append(f'"{title}" {authors.split()[0] if authors else ""}')
        
        return queries
    
    def suggest_classification(self, paper_info: Dict) -> List[str]:
        """基于标题和关键词建议分类"""
        title = paper_info.get('title', '').lower()
        suggestions = []
        
        # 关键词映射
        keyword_mapping = {
            '大语言模型与上下文学习': [
                'in-context learning', 'icl', 'few-shot', 'demonstration',
                'large language model', 'llm', 'gpt', 'prompt'
            ],
            '少样本与零样本NER方法': [
                'zero-shot', 'few-shot', 'named entity recognition',
                'ner', 'entity extraction'
            ],
            '提示工程与示例选择': [
                'prompt', 'demonstration selection', 'example selection',
                'prompting', 'template'
            ],
            '元学习与多智能体方法': [
                'meta-learning', 'multi-agent', 'cooperative',
                'meta', 'agent'
            ],
            '检索增强与指令微调': [
                'retrieval', 'instruction tuning', 'fine-tuning',
                'augmented', 'rag'
            ],
            '综合性研究与工具': [
                'survey', 'comprehensive', 'review', 'tool',
                'framework', 'library'
            ]
        }
        
        for category, keywords in keyword_mapping.items():
            if any(keyword in title for keyword in keywords):
                suggestions.append(category)
        
        return suggestions if suggestions else ['需要人工分类']
    
    def generate_update_report(self, bib_file: str = "references.bib") -> str:
        """生成更新报告"""
        new_keys, deleted_keys = self.check_bib_updates(bib_file)
        
        if not new_keys and not deleted_keys:
            return "没有检测到更新"
        
        report = f"# 知识库更新报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        if new_keys:
            report += f"## 新增文献 ({len(new_keys)}篇)\n\n"
            
            entries = self.parse_bib_file(bib_file)
            for key in new_keys:
                if key in entries:
                    info = self.extract_paper_info(entries[key])
                    suggestions = self.suggest_classification(info)
                    queries = self.generate_search_queries(info)
                    
                    report += f"### {key}\n"
                    report += f"- **标题**: {info['title']}\n"
                    report += f"- **作者**: {info['authors']}\n"
                    report += f"- **年份**: {info['year']}\n"
                    report += f"- **建议分类**: {', '.join(suggestions)}\n"
                    report += f"- **搜索查询**: \n"
                    for query in queries:
                        report += f"  - `{query}`\n"
                    if info['url']:
                        report += f"- **链接**: {info['url']}\n"
                    report += "\n"
        
        if deleted_keys:
            report += f"## 删除文献 ({len(deleted_keys)}篇)\n\n"
            for key in deleted_keys:
                report += f"- {key}\n"
            report += "\n"
        
        report += "## 下一步操作\n\n"
        report += "1. 对每篇新增文献进行网络搜索\n"
        report += "2. 分析技术创新点和核心贡献\n"
        report += "3. 确定分类和在知识库中的位置\n"
        report += "4. 更新主知识库文件\n"
        report += "5. 更新索引文件\n"
        report += "6. 记录更新日志\n"
        
        return report
    
    def validate_knowledge_base(self) -> List[str]:
        """验证知识库的一致性"""
        issues = []
        
        # 检查主文件是否存在
        main_file = self.config.get('knowledge_base', {}).get('main_file')
        if main_file and not Path(main_file).exists():
            issues.append(f"主知识库文件不存在: {main_file}")
        
        # 检查索引文件是否存在
        index_file = self.config.get('knowledge_base', {}).get('index_file')
        if index_file and not Path(index_file).exists():
            issues.append(f"索引文件不存在: {index_file}")
        
        # 检查配置中的方法是否在主文件中存在
        # 这里可以添加更多验证逻辑
        
        return issues

def main():
    """主函数"""
    updater = NERKnowledgeBaseUpdater()
    
    print("NER知识库更新助手")
    print("=" * 50)
    
    # 生成更新报告
    report = updater.generate_update_report()
    print(report)
    
    # 保存报告到文件
    with open(f"update_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md", 'w', encoding='utf-8') as f:
        f.write(report)
    
    # 验证知识库
    issues = updater.validate_knowledge_base()
    if issues:
        print("\n发现的问题:")
        for issue in issues:
            print(f"- {issue}")
    else:
        print("\n知识库验证通过")
    
    # 保存配置
    updater.save_config()

if __name__ == "__main__":
    main()
