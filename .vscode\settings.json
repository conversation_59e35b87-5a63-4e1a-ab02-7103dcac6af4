{"latex-workshop.latex.autoBuild.run": "never", "latex-workshop.latex.rootFile.doNotPrompt": true, "latex-workshop.latex.rootFile.useSubFile": false, "latex-workshop.latex.search.rootFiles.include": ["**/*.tex"], "latex-workshop.latex.search.rootFiles.exclude": ["**/node_modules/**", "**/tmp/**"], "latex-workshop.latex.tools": [{"name": "xelatex", "command": "C:/texlive/2025/bin/windows/xelatex.exe", "args": ["-synctex=1", "-interaction=nonstopmode", "-file-line-error", "%DOCFILE%"]}, {"name": "pdflatex", "command": "C:/texlive/2025/bin/windows/pdflatex.exe", "args": ["-synctex=1", "-interaction=nonstopmode", "-file-line-error", "%DOCFILE%"]}, {"name": "bibtex", "command": "C:/texlive/2025/bin/windows/bibtex.exe", "args": ["%DOCFILE%"]}, {"name": "latexmk", "command": "C:/texlive/2025/bin/windows/latexmk.exe", "args": ["-outdir=%OUTDIR%", "-c", "%DOC%"]}], "latex-workshop.latex.recipes": [{"name": "xelatex", "tools": ["xelatex"]}, {"name": "pdflatex", "tools": ["pdflatex"]}, {"name": "xe->bib->xe->xe", "tools": ["xelatex", "bibtex", "xelatex", "xelatex"]}, {"name": "pdf->bib->pdf->pdf", "tools": ["pdflatex", "bibtex", "pdflatex", "pdflatex"]}], "latex-workshop.latex.clean.fileTypes": ["*.aux", "*.bbl", "*.blg", "*.idx", "*.ind", "*.lof", "*.lot", "*.out", "*.toc", "*.acn", "*.acr", "*.alg", "*.glg", "*.glo", "*.gls", "*.ist", "*.fls", "*.log", "*.fdb_latexmk", "*.synctex.gz"], "latex-workshop.latex.autoClean.run": "onBuilt"}